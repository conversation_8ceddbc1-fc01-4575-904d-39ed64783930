package cloud.demand.app.modules.return_report;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.modules.return_report.model.ReturnFilterValueReq;
import cloud.demand.app.modules.return_report.model.ReturnFilterValueResp;
import cloud.demand.app.modules.return_report.model.ReturnGlobalReq;
import cloud.demand.app.modules.return_report.model.ReturnGlobalResp;
import cloud.demand.app.modules.return_report.model.ReturnMergeQueryReq;
import cloud.demand.app.modules.return_report.model.ReturnMergeQueryResp;
import cloud.demand.app.modules.return_report.model.ReturnPlanDetailResp;
import cloud.demand.app.modules.return_report.model.ReturnedDetailReq;
import cloud.demand.app.modules.return_report.model.ReturnedDetailResp;
import cloud.demand.app.modules.return_report.service.QueryReturnReportService;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import java.util.Date;

/**
 * 物理机退回计划报表
 */
@JsonrpcController("/return-report")
@Slf4j
public class ReturnReportController {

    @Autowired
    private QueryReturnReportService queryReturnReportService;

    /**查询汇总信息（含cvm和物理机信息）*/
    @RequestMapping
    public ReturnGlobalResp queryGlobalInfo(@JsonrpcParam ReturnGlobalReq req) {
        if (req == null) {
            throw new WrongWebParameterException("缺少查询参数");
        }
        Date start = DateUtils.parse(req.getStartDate());
        if (start == null) {
            throw new WrongWebParameterException("缺少startDate参数或startDate格式有误，正确格式:yyyy-MM-dd");
        }
        Date end = DateUtils.parse(req.getEndDate());
        if (end == null) {
            throw new WrongWebParameterException("缺少endDate参数或endDate格式有误，正确格式:yyyy-MM-dd");
        }

        return queryReturnReportService.queryGlobalInfo(req);
    }

    /**物理机查询聚合数据*/
    @RequestMapping
    public ReturnMergeQueryResp queryMergeData(@JsonrpcParam ReturnMergeQueryReq req) {
        if (req == null) {
            throw new WrongWebParameterException("缺少查询参数");
        }
        Date start = DateUtils.parse(req.getStartDate());
        if (start == null) {
            throw new WrongWebParameterException("缺少startDate参数或startDate格式有误，正确格式:yyyy-MM-dd");
        }
        Date end = DateUtils.parse(req.getEndDate());
        if (end == null) {
            throw new WrongWebParameterException("缺少endDate参数或endDate格式有误，正确格式:yyyy-MM-dd");
        }

        return queryReturnReportService.queryMergeData(req);
    }

    @RequestMapping
    public ReturnFilterValueResp queryFilterValue(@JsonrpcParam ReturnFilterValueReq req) {
        if (req == null) {
            throw new WrongWebParameterException("缺少查询参数");
        }
        if (StringTools.isBlank(req.getType())) {
            throw new WrongWebParameterException("缺少type参数");
        }

        return queryReturnReportService.queryFilterValue(req);
    }

    @RequestMapping
    public ReturnedDetailResp queryReturnedDetail(@JsonrpcParam ReturnedDetailReq req) {
        if (req == null) {
            throw new WrongWebParameterException("缺少查询参数");
        }
        Date start = DateUtils.parse(req.getStartDate());
        if (start == null) {
            throw new WrongWebParameterException("缺少startDate参数或startDate格式有误，正确格式:yyyy-MM-dd");
        }
        Date end = DateUtils.parse(req.getEndDate());
        if (end == null) {
            throw new WrongWebParameterException("缺少endDate参数或endDate格式有误，正确格式:yyyy-MM-dd");
        }

        return queryReturnReportService.queryReturnedDetail(req);
    }

    @RequestMapping
    public ReturnPlanDetailResp queryReturnPlanDetail(@JsonrpcParam ReturnedDetailReq req) {
        if (req == null) {
            throw new WrongWebParameterException("缺少查询参数");
        }
        Date start = DateUtils.parse(req.getStartDate());
        if (start == null) {
            throw new WrongWebParameterException("缺少startDate参数或startDate格式有误，正确格式:yyyy-MM-dd");
        }
        Date end = DateUtils.parse(req.getEndDate());
        if (end == null) {
            throw new WrongWebParameterException("缺少endDate参数或endDate格式有误，正确格式:yyyy-MM-dd");
        }

        return queryReturnReportService.queryReturnPlanDetail(req);
    }

}
