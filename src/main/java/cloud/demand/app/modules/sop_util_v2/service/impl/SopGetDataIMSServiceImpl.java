package cloud.demand.app.modules.sop_util_v2.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.entity.demand.DemandCsigServersDO;
import cloud.demand.app.entity.demand.DemandCsigYuntiServersDO;
import cloud.demand.app.modules.mrpv2.alert.util.ValidateUtil;
import cloud.demand.app.modules.soe.model.sql.ObjectSqlBuilder;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import cloud.demand.app.modules.sop_return.utils.SopSelectCaseBuilder;
import cloud.demand.app.modules.sop_review.entity.dws.DwsSopReviewReportHolidayDO;
import cloud.demand.app.modules.sop_review.enums.BgProductAliasType;
import cloud.demand.app.modules.sop_review.service.SopReviewDictService;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import cloud.demand.app.modules.sop_util_v2.anno.MergeBatchReq;
import cloud.demand.app.modules.sop_util_v2.enums.fields.SopIMSFieldEnum;
import cloud.demand.app.modules.sop_util_v2.model.entity.DSMDeliveredDO;
import cloud.demand.app.modules.sop_util_v2.model.entity.DwsSopReviewReportAnyDO;
import cloud.demand.app.modules.sop_util_v2.model.req.ims.SopDSMDeliveredReq;
import cloud.demand.app.modules.sop_util_v2.model.req.ims.SopResourceDataReq;
import cloud.demand.app.modules.sop_util_v2.model.req.ims.SopReviewReq;
import cloud.demand.app.modules.sop_util_v2.service.SopGetDataIMSService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Service
public class SopGetDataIMSServiceImpl implements SopGetDataIMSService {
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    /** cubes */
    @Resource
    private DBHelper ckcubesDBHelper;

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private SopReviewDictService dictService;


    /** MergeBatchReq会合并请求，注意查询的 sql 和返回的结果不同，需要根据 req 的 distribute 来判断具体返回结果 */
    @MergeBatchReq(waitMs = 50)
    @Override
    public List<DwsSopReviewReportAnyDO> getSopReviewOriginData(SopReviewReq req) {
        SopWhereBuilder whereBuilder = new SopWhereBuilder(req, DwsSopReviewReportHolidayDO.class);
        WhereSQL whereSQL = whereBuilder.whereSQL();
        whereSQL.and("resource_type != '物理机' or business_type != '自研上云'"); // 原始报表看 cvm 和物理机剔除自研上云
        String sql = ORMUtils.getSql("/sql/sop_util_v2/ims/sop_review.sql");

        sql = buildBusinessRangeSql(sql,whereSQL,req.getGroupBy());

        return ckcldStdCrpDBHelper.getRaw(DwsSopReviewReportAnyDO.class, sql,whereSQL.getParams());
    }

    /**
     * 构建 sql
     * @param sql 原始 sql，带有变量
     * @param whereSQL 过滤条件
     * @param groupBy 分组
     * @return 构建完成后的 sql
     */
    private String buildBusinessRangeSql(String sql,WhereSQL whereSQL,List<String> groupBy){
        // 业务范围
        Map<String, String> bgAliasMap = dictService.getBgProductAliasMap(BgProductAliasType.BG);
        Map<String, String> bigProductAliasMap = dictService.getBgProductAliasMap(BgProductAliasType.PRODUCT);
        String businessRangeSql = "if(${has_business_range},if(business_type = '自研业务',${bg_alias},${big_product_alias}),null)";
        String bgCase = SopSelectCaseBuilder.build("custom_bg_name", "custom_bg_name", bgAliasMap);
        String bigProductCase = SopSelectCaseBuilder.build("product_big_class", "product_big_class", bigProductAliasMap);
        businessRangeSql = SimpleSqlBuilder.doReplace(businessRangeSql,"bg_alias",bgCase);
        businessRangeSql = SimpleSqlBuilder.doReplace(businessRangeSql,"big_product_alias",bigProductCase);

        sql = SimpleSqlBuilder.doReplace(sql,"business_range",businessRangeSql); // 先替换业务范围
        sql = SimpleSqlBuilder.buildDims(sql, SopIMSFieldEnum.getFieldNames(),groupBy); // 在替换分组（可能不查business_type的分组）
        sql = SimpleSqlBuilder.doReplace(sql,"where",whereSQL.getSQL());
        return sql;
    }

    @Override
    public List<DSMDeliveredDO> getDSMDelivered(SopDSMDeliveredReq req) {
        String sql = ORMUtils.getSql("/sql/sop_util_v2/ims/ims_delivered_num.sql");
        String cvmStatTime = req.getCvmStatTime();
        ObjectSqlBuilder sqlBuilder = new ObjectSqlBuilder();
        sqlBuilder.addSqlParams(req);
        sql = SimpleSqlBuilder.doReplace(sql,"int_stat_time",cvmStatTime.replace("-",""));
        sql = SimpleSqlBuilder.buildDims(sql, SopIMSFieldEnum.getFieldNames(),req.getGroupBy());
        sql = sqlBuilder.build(sql);
        return ckcubesDBHelper.getRaw(DSMDeliveredDO.class, sql);
    }

    @Override
    public List<DemandCsigServersDO> getResourceService(SopResourceDataReq req) {
        List<String> valid = ValidateUtil.valid(req);
        if (valid !=null){
            throw new IllegalArgumentException("参数错误：" + valid + "");
        }
        req.setDay(DateUtils.format(SoeCommonUtils.minDate(DateUtils.parseLocalDate(req.getDay()), LocalDate.now().plusDays(-1)))); // day 最大为昨天
        SopWhereBuilder whereBuilder = new SopWhereBuilder(req, DemandCsigServersDO.class);
        WhereSQL whereSQL = whereBuilder.whereSQL();
        whereSQL.and("deleted = 0");
        return demandDBHelper.getAll(DemandCsigServersDO.class, whereSQL.getSQL(), whereSQL.getParams());
    }

    @Override
    public List<ServerInfo> getResourceYuntiService(SopResourceDataReq req) {
        List<String> valid = ValidateUtil.valid(req);
        if (valid !=null){
            throw new IllegalArgumentException("参数错误：" + valid + "");
        }
        String sql = ORMUtils.getSql("/sql/sop_util_v2/ims/resource_view_yunti_server.sql");
        sql = SimpleSqlBuilder.doReplace(sql,"stat_time",DateUtils.format(SoeCommonUtils.minDate(DateUtils.parseLocalDate(req.getDay()), LocalDate.now().plusDays(-1))));
        return ckcubesDBHelper.getRaw(ServerInfo.class, sql);
    }

    @Data
    public static class ServerInfo {
        @Column("bg_name")
        private String bgName;
        @Column("dept_name")
        private String deptName;
        @Column("plan_product_name")
        private String planProductName;
        @Column("customhouse_title")
        private String customhouseTitle;
        @Column("country_name")
        private String countryName;
        @Column("core_num")
        private BigDecimal coreNum;
    }
}
