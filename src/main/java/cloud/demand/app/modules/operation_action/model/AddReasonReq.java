package cloud.demand.app.modules.operation_action.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class AddReasonReq {
    @NotNull
    private String reasonType;
    private String reasonDetail;
    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;
    @NotNull
    private String instanceType;
    @NotNull
    private String zoneName;
}
