package cloud.demand.app.modules.soe.dto.other;

import cloud.demand.app.modules.soe.enums.MockStatusEnum;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.time.LocalDate;

/** 创建或者更新模拟数据 */
@Data
public class SoeMockCreateOrUpdateReq {
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 产品类型 */
    @NotNull(message = "产品类型不能为空")
    @Pattern(regexp = "CVM|GPU",message = "产品类型只能为CVM或者GPU")
    private String productType;

    /** 完整标签 */
    private String fullLabel;

    /** 一级标签 */
    @NotEmpty(message = "一级标签不能为空")
    private String oneLabel;

    /** 二级标签 （有无二级标签的：期初库存）*/
//    @NotEmpty(message = "二级标签不能为空")
    private String twoLabel;


    /** 境内外 */
    @NotEmpty(message = "境内外不能为空")
    @Pattern(regexp = "境内|境外")
    private String customhouseTitle;

    /** 区域 */
    @NotEmpty(message = "区域不能为空")
    private String areaName;

    /** 地域 */
    @NotEmpty(message = "区域不能为空")
    private String regionName;

    /** 可用区 */
    @NotEmpty(message = "可用区不能为空")
    private String zoneName;

    /** 时间类型 {@link cloud.demand.app.modules.soe.enums.DateTypeEnum} */
    @NotEmpty(message = "时间类型不能为空")
    @Pattern(regexp = "月|周",message = "时间维度只能为月或者周")
    private String dateType;

    /** 切片日期（仅仅期初库存需要） */
    private String statTime;

    /** 年月 */
    private String yearMonth;

    /** 年周 */
    private String yearWeek;

    /** 实例类型（GPU的为卡型） */
    private String instanceType;

    /** 实例族 */
    @NotEmpty(message = "实例族不能为空")
    private String ginsFamily;

    /** 卡型 */
    private String gpuType;

    /** 核心数（增量） */
    @NotNull(message = "调整数量不能为空")
    private BigDecimal coreNum;

    /** 模拟原因<br/>Column: [change_result] */
    @NotEmpty(message = "模拟原因不能为空")
    private String mockResult;

    /** 起始生效时间 */
    private String startEffectiveTime;

    /** 结束生效时间 */
    private String endEffectiveTime;

    /** 状态：OPEN:开启，CLOSE:关闭<br/>Column: [status] */
    private String status = MockStatusEnum.getDef();

    /** cas乐观锁版本 */
    private Integer casVersion;
}
