package cloud.demand.app.modules.plan_detail.service.impl;

import cloud.demand.app.entity.rrp.ReportPlanDetailDO;
import cloud.demand.app.modules.common.enums.ComputeTypeEnum;
import cloud.demand.app.modules.common.enums.PlanIndicatorEnum;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.plan_detail.enums.PlanDetailMetalIndicatorEnum;
import cloud.demand.app.modules.plan_detail.model.sale.ExternalBillingScaleDTO;
import cloud.demand.app.modules.plan_detail.model.sale.InternalApplyScaleDTO;
import cloud.demand.app.modules.plan_detail.model.sale.InternalRenderDTO;
import cloud.demand.app.modules.plan_detail.model.sale.InternalSuppDTO;
import cloud.demand.app.modules.plan_detail.service.PlanDetailCommonService;
import cloud.demand.app.modules.plan_detail.service.PlanSaleDetailService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.nutz.lang.Lang;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class PlanSaleDetailServiceImpl implements PlanSaleDetailService {

    @Resource
    private DBHelper planDBHelper;
    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    PlanDetailCommonService planDetailCommonService;

    public void getAndSaveCvmSaleData(String statTime) {
        planDetailCommonService.saveCvmDataToDB(generateCvmExternalBillingScaleData(statTime));
        planDetailCommonService.saveCvmDataToDB(generateCvmInternalApplyData(statTime));
    }

    @Override
    public void getAndSaveMetalSaleData(String statTime) {
        planDetailCommonService.saveMetalDataToDB(generateMetalExternalBillingScaleData(statTime));
        planDetailCommonService.saveMetalDataToDB(generateMetalInternalApplyData(statTime));
    }


    /**
     * 生成【CVM产品】外部计费规模的两个指标detail数据
     */
    private List<ReportPlanDetailDO> generateCvmExternalBillingScaleData(String statTime){
        List<ReportPlanDetailDO> result = new ArrayList<>();
        ArrayList<ComputeTypeEnum> computeTypes = Lang.list(ComputeTypeEnum.CPU, ComputeTypeEnum.GPU);
        ArrayList<PlanIndicatorEnum> indicators =
                Lang.list(PlanIndicatorEnum.SALE_CVM_BILLING_SCALE, PlanIndicatorEnum.SALE_LH_SCALE);
        for (ComputeTypeEnum computeType : computeTypes) {
            List<ExternalBillingScaleDTO> dtoList = generateCvmExternalSaleDTOList(statTime, computeType);
            if (ListUtils.isEmpty(dtoList)) {
                for (PlanIndicatorEnum e : indicators) {
                    ReportPlanDetailDO planDetailDO = e.toReportPlanDetailDO();
                    planDetailDO.setStatTime(DateUtils.parse(statTime));
                    planDetailDO.setProductType(ProductTypeEnum.CVM.getCode());
                    planDetailDO.setComputeType(computeType.getCode());
                    planDetailDO.setCores(BigDecimal.ZERO);
                    result.add(planDetailDO);
                    continue;
                }
            }
            for (ExternalBillingScaleDTO dto : dtoList) {
                for (PlanIndicatorEnum indicator : indicators) {
                    ReportPlanDetailDO planDetailDO = indicator.toReportPlanDetailDO();
                    planDetailCommonService.fillReportPlanDetailDO(planDetailDO, ProductTypeEnum.CVM, computeType, dto, statTime);
                    BigDecimal indicatorValue = BigDecimal.ZERO;
                    BigDecimal gpucard = BigDecimal.ZERO;
                    switch (indicator) {
                        case SALE_CVM_BILLING_SCALE:    //  CVM计费规模
                            indicatorValue = dto.getCvmBillingScaleCore();
                            gpucard = dto.getCvmGpu();
                            break;
                        case SALE_LH_SCALE:     //  Lighthouse规模
                            indicatorValue = dto.getLhScaleCore();
                            gpucard = dto.getLhGpu();
                            break;
                    }
                    planDetailDO.setCores(indicatorValue);
                    planDetailDO.setLogicNum(gpucard);
                    result.add(planDetailDO);
                }
            }
            // clean重复数据，这里直接删除
            rrpDBHelper.delete(ReportPlanDetailDO.class,
                    "where stat_time = ? and indicator_code in (?) and product_type = ? and compute_type = ?",
                    statTime, ListUtils.transform(indicators, PlanIndicatorEnum::getCode),
                    ProductTypeEnum.CVM.getCode(), computeType.getCode());
        }
        return result;
    }

    /**
     * 生成【裸金属】产品外部计费规模的两个指标detail数据
     */
    private List<ReportPlanDetailDO> generateMetalExternalBillingScaleData(String statTime){
        List<ReportPlanDetailDO> result = new ArrayList<>();
        ArrayList<ComputeTypeEnum> computeTypes = Lang.list(ComputeTypeEnum.CPU, ComputeTypeEnum.GPU);

        for (ComputeTypeEnum computeType : computeTypes) {
            List<ExternalBillingScaleDTO> dtoList = generateMetalExternalSaleDTOList(statTime, computeType);
            if (ListUtils.isEmpty(dtoList)) {
                    ReportPlanDetailDO planDetailDO = PlanDetailMetalIndicatorEnum.SALE_OUT.toReportPlanDetailDO();
                    planDetailDO.setStatTime(DateUtils.parse(statTime));
                    planDetailDO.setProductType(ProductTypeEnum.METAL.getCode());
                    planDetailDO.setComputeType(computeType.getCode());
                    planDetailDO.setCores(BigDecimal.ZERO);
                    result.add(planDetailDO);
                    continue;
            }
            for (ExternalBillingScaleDTO dto : dtoList) {
                ReportPlanDetailDO planDetailDO = PlanDetailMetalIndicatorEnum.SALE_OUT.toReportPlanDetailDO();
                planDetailCommonService.fillReportPlanDetailDO(planDetailDO, ProductTypeEnum.METAL, computeType, dto, statTime);
                planDetailDO.setCores(dto.getCvmBillingScaleCore());
                result.add(planDetailDO);

            }
            // clean重复数据，这里直接删除
            rrpDBHelper.delete(ReportPlanDetailDO.class,
                    "where stat_time = ? and indicator_code in (?) and product_type = ? and compute_type = ?",
                    statTime, PlanDetailMetalIndicatorEnum.SALE_OUT.getCode(),
                    ProductTypeEnum.METAL.getCode(), computeType.getCode());
        }
        return result;
    }

    /**
     * 生成【CVM】内部领用规模指标
     */
    private List<ReportPlanDetailDO> generateCvmInternalApplyData(String statTime){
        List<ReportPlanDetailDO> result = new ArrayList<>();
        ArrayList<PlanIndicatorEnum> indicators =
                Lang.list(PlanIndicatorEnum.SALE_CSIG_APPLY, PlanIndicatorEnum.SALE_OTHER_BG_APPLY);
        ArrayList<ComputeTypeEnum> computeTypes = Lang.list(ComputeTypeEnum.CPU, ComputeTypeEnum.GPU);

        for (ComputeTypeEnum computeType : computeTypes) {
            List<InternalApplyScaleDTO> dtoList = generateCvmInternalApplyScaleDTOList(statTime, computeType);
            if (ListUtils.isEmpty(dtoList)) {
                for (PlanIndicatorEnum e : indicators) {
                    ReportPlanDetailDO planDetailDO = e.toReportPlanDetailDO();
                    planDetailDO.setStatTime(DateUtils.parse(statTime));
                    planDetailDO.setProductType(ProductTypeEnum.CVM.getCode());
                    planDetailDO.setComputeType(computeType.getCode());
                    planDetailDO.setCores(BigDecimal.ZERO);
                    result.add(planDetailDO);
                    continue;
                }
            }

            for (InternalApplyScaleDTO dto : dtoList) {
                for (PlanIndicatorEnum indicator : indicators) {
                    ReportPlanDetailDO planDetailDO = indicator.toReportPlanDetailDO();
                    planDetailCommonService.fillReportPlanDetailDO(planDetailDO, ProductTypeEnum.CVM, computeType, dto, statTime);
                    BigDecimal indicatorValue = BigDecimal.ZERO;
                    BigDecimal gpuCard = BigDecimal.ZERO;

                    switch (indicator) {
                        case SALE_CSIG_APPLY:    // CSIG领用
                            indicatorValue = dto.getFreeCpuCsig();
                            gpuCard = dto.getGpuCsig();
                            break;
                        case SALE_OTHER_BG_APPLY:     //  其他BG领用
                            indicatorValue = dto.getFreeCpuOther();
                            gpuCard = dto.getGpuOther();
                            break;
                    }
                    planDetailDO.setCores(indicatorValue);
                    planDetailDO.setLogicNum(gpuCard);
                    result.add(planDetailDO);
                }
            }

            //  CVM产品 CSIG领用包含支撑区的数据, 裸金属产品不包括
            List<ReportPlanDetailDO> suppList = generateInternalSuppData(statTime, computeType);
            if (ListUtils.isNotEmpty(suppList)) {
                result.addAll(suppList);
            }
            //  CVM产品 渲染领用部分的数据
            List<ReportPlanDetailDO> renderList = generateInternalRenderData(statTime, computeType);
            if (ListUtils.isNotEmpty(renderList)) {
                result.addAll(renderList);
            }
            // clean重复数据，这里直接删除
            rrpDBHelper.delete(ReportPlanDetailDO.class,
                    "where stat_time = ? and indicator_code in (?) and product_type = ? and compute_type = ?",
                    statTime, ListUtils.transform(indicators, PlanIndicatorEnum::getCode), ProductTypeEnum.CVM.getCode(),
                    computeType.getCode());
        }
        return result;
    }

    /**
     * 生成内部领用规模指标
     */
    private List<ReportPlanDetailDO> generateMetalInternalApplyData(String statTime){
        List<ReportPlanDetailDO> result = new ArrayList<>();
        ArrayList<PlanDetailMetalIndicatorEnum> indicators =
                Lang.list(PlanDetailMetalIndicatorEnum.SALE_CSIG_APPLY, PlanDetailMetalIndicatorEnum.SALE_OTHER_BG_APPLY);
        ArrayList<ComputeTypeEnum> computeTypes = Lang.list(ComputeTypeEnum.CPU, ComputeTypeEnum.GPU);

        for (ComputeTypeEnum computeType : computeTypes) {
            List<InternalApplyScaleDTO> dtoList = generateMetalInternalApplyScaleDTOList(statTime, computeType);
            if (ListUtils.isEmpty(dtoList)) {
                for (PlanDetailMetalIndicatorEnum e : indicators) {
                    ReportPlanDetailDO planDetailDO = e.toReportPlanDetailDO();
                    planDetailDO.setStatTime(DateUtils.parse(statTime));
                    planDetailDO.setProductType(ProductTypeEnum.METAL.getCode());
                    planDetailDO.setComputeType(computeType.getCode());
                    planDetailDO.setCores(BigDecimal.ZERO);
                    result.add(planDetailDO);
                    continue;
                }
            }

            for (InternalApplyScaleDTO dto : dtoList) {
                for (PlanDetailMetalIndicatorEnum indicator : indicators) {
                    ReportPlanDetailDO planDetailDO = indicator.toReportPlanDetailDO();
                    planDetailCommonService.fillReportPlanDetailDO(planDetailDO, ProductTypeEnum.METAL, computeType, dto, statTime);
                    BigDecimal indicatorValue = BigDecimal.ZERO;
                    switch (indicator) {
                        case SALE_CSIG_APPLY:    // CSIG领用
                            indicatorValue = dto.getFreeCpuCsig();
                            break;
                        case SALE_OTHER_BG_APPLY:     //  其他BG领用
                            indicatorValue = dto.getFreeCpuOther();
                            break;
                    }
                    planDetailDO.setCores(indicatorValue);
                    result.add(planDetailDO);
                }
            }
            // clean重复数据，这里直接删除
            rrpDBHelper.delete(ReportPlanDetailDO.class,
                    "where stat_time = ? and indicator_code in (?) and product_type = ? and compute_type = ?",
                    statTime, ListUtils.transform(indicators, PlanDetailMetalIndicatorEnum::getCode),
                    ProductTypeEnum.METAL.getCode(), computeType.getCode());
        }
        return result;
    }



    /**
     * 通过支撑区数据的DTO转换出ReportPlanDetailDO对象
     */
    private List<ReportPlanDetailDO> generateInternalSuppData(String statTime, ComputeTypeEnum computeType){
        List<ReportPlanDetailDO> result = new ArrayList<>();
        List<InternalSuppDTO> dtoList = generateInternalSuppDTOList(statTime, computeType);
        if (ListUtils.isEmpty(dtoList)) {
            return Lang.list();
        }

        for (InternalSuppDTO dto : dtoList) {
            ReportPlanDetailDO planDetailDO = PlanIndicatorEnum.SALE_CSIG_APPLY.toReportPlanDetailDO();
            planDetailCommonService.fillReportPlanDetailDO(planDetailDO, ProductTypeEnum.CVM, computeType, dto, statTime);
            planDetailDO.setCores(dto.getFreeCpuSupp());
            planDetailDO.setLogicNum(dto.getGpu());
            result.add(planDetailDO);
        }
        return result;
    }

    /**
     * 通过渲染领用数据的DTO转换出ReportPlanDetailDO对象
     */
    private List<ReportPlanDetailDO> generateInternalRenderData(String statTime, ComputeTypeEnum computeType){
        List<ReportPlanDetailDO> result = new ArrayList<>();
        List<InternalRenderDTO> dtoList = generateInternalRenderDTOList(statTime, computeType);
        if (ListUtils.isEmpty(dtoList)) {
            return Lang.list();
        }

        for (InternalRenderDTO dto : dtoList) {
            ReportPlanDetailDO planDetailDO = PlanIndicatorEnum.SALE_CSIG_APPLY.toReportPlanDetailDO();
            planDetailCommonService.fillReportPlanDetailDO(planDetailDO, ProductTypeEnum.CVM, computeType, dto, statTime);
            planDetailDO.setCores(dto.getFreeCpuRender());
            planDetailDO.setLogicNum(dto.getGpu());
            result.add(planDetailDO);
        }
        return result;
    }


    /**
     * 获取外部售卖规模DTO对象
     */
    private List<ExternalBillingScaleDTO> generateCvmExternalSaleDTOList(String statTime, ComputeTypeEnum computeType){
        String sql = "SELECT a.zoneid `zoneid`," +
                "       a.cvmtype `cvmtype`," +
                "       SUM(a.billcpu) `CVM计费规模`," +
                "       SUM(a.billcpu_lh) `Lighthouse规模`," +
                "       ifnull(SUM(a.billcpu * 100 / b.cpu * b.gpu / b.gpuratio), 0) `cvm_gpu`," +
                "       ifnull(SUM(a.billcpu_lh * 100 / b.cpu * b.gpu / b.gpuratio), 0) `lh_gpu`";
        String fromTable = " FROM daily_zone_cvmtype_ginstype_p2p a " +
                "         LEFT JOIN `static_ginstype` b ON a.ginstype = b.ginstype WHERE a.stattime = ?";

        //  CVM
        String condCvmCpu = " AND a.ginstype in (select ginstype from static_ginstype where biztype = 'cvm' and gpu = 0)";
        String condCvmGpu = " AND a.ginstype in (select ginstype from static_ginstype where biztype = 'cvm' and gpu > 0)";

        String groupBy = " group by zoneid, cvmtype";

        StringBuilder builder = new StringBuilder();
        builder.append(sql).append(fromTable);

        if (Objects.equals(computeType, ComputeTypeEnum.CPU)){
            builder.append(condCvmCpu);
        }else if (Objects.equals(computeType, ComputeTypeEnum.GPU)){
            builder.append(condCvmGpu);
        }

        builder.append(groupBy);

        List<ExternalBillingScaleDTO> raw = planDBHelper.getRaw(ExternalBillingScaleDTO.class, builder.toString(), statTime);
        if (ListUtils.isEmpty(raw)){
            return Lang.list();
        }
        return raw;
    }

    /**
     * 获取外部售卖规模DTO对象
     */
    private List<ExternalBillingScaleDTO> generateMetalExternalSaleDTOList(String statTime, ComputeTypeEnum computeType){
        String sql = "SELECT zoneid, cvmtype," +
                "       SUM(billcpu)    AS 'CVM计费规模'";
        String fromTable = " FROM daily_zone_cvmtype_ginstype_p2p WHERE stattime = ?";

        // Metal
        String condMetalCpu = " AND ginstype in (select ginstype from static_ginstype where biztype = 'baremetal' and gpu = 0)";
        String condMetalGpu = " AND ginstype in (select ginstype from static_ginstype where biztype = 'baremetal' and gpu > 0)";

        String groupBy = " group by zoneid, cvmtype";

        StringBuilder builder = new StringBuilder();
        builder.append(sql).append(fromTable);

        if (Objects.equals(computeType, ComputeTypeEnum.CPU)){
            builder.append(condMetalCpu);
        }else if (Objects.equals(computeType, ComputeTypeEnum.GPU)){
            builder.append(condMetalGpu);
        }

        builder.append(groupBy);

        List<ExternalBillingScaleDTO> raw = planDBHelper.getRaw(ExternalBillingScaleDTO.class, builder.toString(), statTime);
        if (ListUtils.isEmpty(raw)){
            return Lang.list();
        }
        return raw;
    }


    /**
     * 获取【CVM】内部领用规模：CSIG领用、其他BG领用的DTO对象
     */
    private List<InternalApplyScaleDTO> generateCvmInternalApplyScaleDTOList(String statTime, ComputeTypeEnum computeType){
        String sql = "SELECT a.cvmtype `cvmtype`, a.zoneid `zoneid`," +
                "       SUM(freecpu_csig)  freecpu_csig," +
                "       SUM(freecpu_other) freecpu_other," +
                "       ifnull(SUM(a.freecpu_csig*100/b.cpu*b.gpu/b.gpuratio), 0) `gpu_csig`," +
                "       ifnull(SUM(a.freecpu_other*100/b.cpu*b.gpu/b.gpuratio), 0) `gpu_other`";

        String fromTable = " FROM daily_zone_cvmtype_ginstype_p2p a " +
                "         LEFT JOIN `static_ginstype` b ON a.ginstype = b.ginstype WHERE a.stattime = ?";

        //  CVM
        String condCvmCpu = " AND a.ginstype in (select ginstype from static_ginstype where biztype = 'cvm' and gpu = 0)";
        String condCvmGpu = " AND a.ginstype in (select ginstype from static_ginstype where biztype = 'cvm' and gpu > 0)";

        String groupBy = " group by cvmtype, zoneid";

        StringBuilder builder = new StringBuilder();
        builder.append(sql).append(fromTable);

        if (Objects.equals(computeType, ComputeTypeEnum.CPU)){
            builder.append(condCvmCpu);
        }else if (Objects.equals(computeType, ComputeTypeEnum.GPU)){
            builder.append(condCvmGpu);
        }

        builder.append(groupBy);

        List<InternalApplyScaleDTO> raw = planDBHelper.getRaw(InternalApplyScaleDTO.class, builder.toString(), statTime);
        if (ListUtils.isEmpty(raw)){
            return Lang.list();
        }
        return raw;
    }

    /**
     * 获取【裸金属】内部领用规模：CSIG领用、其他BG领用的DTO对象
     */
    private List<InternalApplyScaleDTO> generateMetalInternalApplyScaleDTOList(String statTime, ComputeTypeEnum computeType){
        String sql = "SELECT cvmtype, zoneid," +
                "       SUM(freecpu_csig)  freecpu_csig," +
                "       SUM(freecpu_other) freecpu_other";

        String fromTable = " FROM daily_zone_cvmtype_ginstype_p2p WHERE stattime = ?";

        // Metal
        String condMetalCpu = " AND ginstype in (select ginstype from static_ginstype where biztype = 'baremetal' and gpu = 0)";
        String condMetalGpu = " AND ginstype in (select ginstype from static_ginstype where biztype = 'baremetal' and gpu > 0)";

        String groupBy = " group by cvmtype, zoneid";

        StringBuilder builder = new StringBuilder();
        builder.append(sql).append(fromTable);

        if (Objects.equals(computeType, ComputeTypeEnum.CPU)){
            builder.append(condMetalCpu);
        }else if (Objects.equals(computeType, ComputeTypeEnum.GPU)){
            builder.append(condMetalGpu);
        }

        builder.append(groupBy);

        List<InternalApplyScaleDTO> raw = planDBHelper.getRaw(InternalApplyScaleDTO.class, builder.toString(), statTime);
        if (ListUtils.isEmpty(raw)){
            return Lang.list();
        }
        return raw;
    }

    /**
     * 获取内部领用规模：支撑区的DTO对象
     */
    private List<InternalSuppDTO> generateInternalSuppDTOList(String statTime, ComputeTypeEnum computeType){
        String sql = "SELECT a.stdtype `stdtype`," +
                "       a.zoneid `zoneid`," +
                "       SUM(b.`cpu_physic` / 100 * a.`hostcnt`) `支撑区核心数`, " +
                "       COALESCE(SUM(b.`gpucard`*a.`hostcnt`),0) `gpu`";

        String fromTable = " FROM daily_cmdb_zoneid_stdtype_biztype_type a" +
                "         LEFT JOIN `static_cvmtype` b ON a.stdtype = b.`cvmtype`" +
                " WHERE a.stattime = ? AND a.biztype = 'SUPP'";
        String condCvm = " AND  b.gpucard = 0";
        String condGpu = " AND  b.gpucard > 0";
        String groupBy = " group by stdtype, zoneid";

        StringBuilder builder = new StringBuilder();
        if (Objects.equals(computeType, ComputeTypeEnum.CPU)) {
            builder.append(sql).append(fromTable).append(condCvm).append(groupBy);
        }else if (Objects.equals(computeType, ComputeTypeEnum.GPU)){
            builder.append(sql).append(fromTable).append(condGpu).append(groupBy);
        }

        List<InternalSuppDTO> raw = planDBHelper.getRaw(InternalSuppDTO.class, builder.toString(), statTime);
        if (ListUtils.isEmpty(raw)){
            return Lang.list();
        }
        return raw;
    }


    /**
     * 获取内部领用规模：渲染领用的DTO对象
     */
    private List<InternalRenderDTO> generateInternalRenderDTOList(String statTime, ComputeTypeEnum computeType){
        String sql = "SELECT a.stdtype `stdtype`," +
                "       a.zoneid `zoneid`," +
                "       SUM(b.`cpu_physic` / 100 * a.`hostcnt`) `渲染申领核心数`, " +
                "       COALESCE(SUM(b.`gpucard`*a.`hostcnt`),0) `gpu`";

        String fromTable = " FROM daily_cmdb_zoneid_stdtype_biztype_type a" +
                "         LEFT JOIN `static_cvmtype` b ON a.stdtype = b.`cvmtype`" +
                " WHERE a.stattime = ? AND a.biztype = 'GOCP'";
        String condCvm = " AND  b.gpucard = 0";
        String condGpu = " AND  b.gpucard > 0";
        String groupBy = " group by stdtype, zoneid";

        StringBuilder builder = new StringBuilder();
        if (Objects.equals(computeType, ComputeTypeEnum.CPU)) {
            builder.append(sql).append(fromTable).append(condCvm).append(groupBy);
        }else if (Objects.equals(computeType, ComputeTypeEnum.GPU)){
            builder.append(sql).append(fromTable).append(condGpu).append(groupBy);
        }
        List<InternalRenderDTO> raw = planDBHelper.getRaw(InternalRenderDTO.class, builder.toString(), statTime);
        if (ListUtils.isEmpty(raw)){
            return Lang.list();
        }
        return raw;
    }

}
