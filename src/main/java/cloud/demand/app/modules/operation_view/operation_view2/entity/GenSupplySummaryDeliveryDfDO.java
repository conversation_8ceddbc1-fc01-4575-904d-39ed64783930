package cloud.demand.app.modules.operation_view.operation_view2.entity;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 【安全库存 - 供应汇总表】生成所需要的交付信息
 */
@Data
public class GenSupplySummaryDeliveryDfDO {
    /** 产品类型 */
    @Column("product_type")
    private String productType;

    private String customhouseTitle;

    private String areaName;

    private String regionName;

    private String zoneName;

    private String instanceType;

    @Column("cores")
    private BigDecimal cores;

    @Column("city_zone")
    private String cityZone;

    @Column("submit_time")
    private String submitTime;

    @Column("campus_name")
    private String campusName;

    @Column("device_type")
    private String deviceType;

    @Column("total_num")
    private BigDecimal totalNum;

    @Column("total_delivery_time")
    private String totalDeliveryTime;

    @Column("expect_delivery_date")
    private String expectDeliveryDate;

    @Column("promise_delivery_time")
    private String promiseDeliveryTime;

    @Column("logic_cores")
    private BigDecimal logicCores;

    @Column("quota_id")
    private String quotaId;

    /**
     * 生产状态
     */
    @Column("produce_status")
    private String produceStatus;

    @Column("xy_customer_name")
    private String customerName;

    @Column("xy_industry")
    private String industry;
}
