package cloud.demand.app.modules.operation_view.inventory_health.dto.future;

import cloud.demand.app.modules.operation_view.inventory_health.dto.CommonQueryConditionDTO;
import lombok.Data;

import java.util.List;

/**
 * 库存预测请求POJO,这里继承以便后续扩展
 */
@Data
public class InventoryHealthFutureReq extends CommonQueryConditionDTO {

    /**
     * 统计日期，如果没有传，用最新的日期，这个参数目前前端还没有，保留给后面用
     */
    private String statTime;

    /**
     * 主力园区
     */
    private Boolean isMainZone;

    /**
     * 主力机型
     */
    private Boolean isMainInstanceType;

    /** 园区类型: 主力园区、在售非主力园区、其他 */
    private List<String> zoneCategory;

    /** 机型类型: 主力机型、在售非主力机型、其他 */
    private List<String> instanceTypeCategory;

}
