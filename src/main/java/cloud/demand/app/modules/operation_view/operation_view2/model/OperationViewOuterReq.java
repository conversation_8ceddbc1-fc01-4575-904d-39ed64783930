package cloud.demand.app.modules.operation_view.operation_view2.model;

import cloud.demand.app.modules.common.valid.EnumValid;
import cloud.demand.app.modules.operation_view.common.enums.SafetyInventoryAlgorithmEnum;
import lombok.Data;

@Data
public class OperationViewOuterReq extends OperationViewReq2 {

    /**
     * 安全库存算法，必填
     * @see SafetyInventoryAlgorithmEnum
     */
    @EnumValid(enumClass = SafetyInventoryAlgorithmEnum.class)
    private String algorithm;

}
