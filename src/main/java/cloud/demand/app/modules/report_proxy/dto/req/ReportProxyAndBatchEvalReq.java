package cloud.demand.app.modules.report_proxy.dto.req;

import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/** 报表请求参数 */
@Data
public class ReportProxyAndBatchEvalReq {

    @NotEmpty(message = "valueScript不能为空")
    @Valid
    private List<ReportProxyAndEvalReq> reqList;

    /** 值校验，入参为 req 返回的每个 values组成的集合 */
    private List<String> resultScriptList;

    /** 是否需要全部通过 */
    private Boolean needAllPaas;
}
