package cloud.demand.app.modules.report_proxy.modules.task_monitor.web;


import cloud.demand.app.modules.report_proxy.dto.req.GlobalArgsReq;
import cloud.demand.app.modules.report_proxy.modules.task_monitor.service.GlobalSqlArgsService;
import cloud.demand.app.modules.sop.domain.ReturnT;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@Slf4j
@Tag(name = "全局参数", description = "全局参数")
@JsonrpcController("/report/global-args")
public class GlobalSqlArgsController {
    @Resource
    private GlobalSqlArgsService globalSqlArgsService;

    @Operation(summary = "获取变量")
    @RequestMapping
    public ReturnT<String> getArgs(@JsonrpcParam GlobalArgsReq req) {
        String ret = globalSqlArgsService.getArgs(req);
        return ReturnT.ok(ret);
    }
}
