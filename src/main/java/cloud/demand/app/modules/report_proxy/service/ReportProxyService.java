package cloud.demand.app.modules.report_proxy.service;


import cloud.demand.app.modules.report_proxy.dto.req.ReportProxyAndBatchEvalReq;
import cloud.demand.app.modules.report_proxy.dto.req.ReportProxyAndEvalReq;
import cloud.demand.app.modules.report_proxy.dto.req.ReportProxyReq;
import cloud.demand.app.modules.report_proxy.dto.req.ReportScriptInfoReq;
import cloud.demand.app.modules.report_proxy.dto.req.ReportScriptProxyAndBatchEvalReq;
import cloud.demand.app.modules.report_proxy.dto.req.ReportSqlAnalysisSqlReq;
import cloud.demand.app.modules.report_proxy.dto.req.ReportSqlReq;
import cloud.demand.app.modules.report_proxy.dto.resp.ReportAnalysisSqlResp;
import cloud.demand.app.modules.report_proxy.dto.resp.ReportBatchEVALResp;
import cloud.demand.app.modules.report_proxy.dto.resp.ReportEVALResp;
import cloud.demand.app.modules.report_proxy.dto.resp.ReportProxyReqScheduleResp;
import cloud.demand.app.modules.report_proxy.dto.resp.ReportScriptBatchEVALResp;
import cloud.demand.app.modules.report_proxy.dto.resp.ReportSqlResp;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/** 报表代理的 抽象 */
public interface ReportProxyService {

    /**
     * 代理报表请求
     * @param req 请求
     * @return 报表请求返回值
     */
    Object proxyReq(ReportProxyReq req);

    /**
     * 报表 sql 分析
     * @param req 请求
     * @return sql 分析结果
     */
    ReportAnalysisSqlResp sqlInfoWithProxy(ReportSqlAnalysisSqlReq req);

    /**
     * 报表 sql 分析
     * @param req 请求
     * @return sql 分析结果
     */
    ReportAnalysisSqlResp sqlInfo(ReportSqlAnalysisSqlReq req);

    /**
     * 代理报表请求，并解析结果
     * @param req 请求
     * @return 报表请求返回值，并解析结果
     * */
    ReportEVALResp proxyReqAndEval(ReportProxyAndEvalReq req);


    /**
     * 批量代理报表请求，并解析结果，支持多个请求进行对比
     * @param req 请求
     * @return 多个报表返回值，和对比结果
     */
    ReportBatchEVALResp proxyReqAndBatchEval(ReportProxyAndBatchEvalReq req);


    /**
     * 代理 sql 执行查询
     * @param req 请求
     * @return sql 查询结果
     */
    ReportSqlResp proxySql(ReportSqlReq req);


    /**
     * 展示 dbHelper 列表
     * @return
     */
    List<String> listDBHelper();


    /**
     *  代理脚本请求，并解析结果
     * @param req 请求
     * @return 报表请求返回值，并解析结果
     */
    ReportScriptBatchEVALResp proxyScriptReqAndBatchEval(ReportScriptProxyAndBatchEvalReq req);


    /**
     *  代理脚本请求
     * @param req 请求
     * @return 报表请求返回值，并解析结果
     */
    TreeMap<String, List<String>> proxyScript(ReportScriptInfoReq req);

    /** 报表代理定时任务信息 */
    ReportProxyReqScheduleResp proxyReqScheduleInfo();

}
