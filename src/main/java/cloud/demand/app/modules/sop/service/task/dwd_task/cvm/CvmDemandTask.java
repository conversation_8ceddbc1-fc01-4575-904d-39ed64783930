package cloud.demand.app.modules.sop.service.task.dwd_task.cvm;

import cloud.demand.app.common.utils.EnvUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.domain.SopServerPageReq;
import cloud.demand.app.modules.sop.domain.http.SopCvmDemandResList;
import cloud.demand.app.modules.sop.domain.http.SopPageRes;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportDemandCvmDO;
import cloud.demand.app.modules.sop.entity.other.DwdYuntiCvmDemandForecastItemDfDO;
import cloud.demand.app.modules.sop.entity.other.YuntiDemandCvmItemDO;
import cloud.demand.app.modules.sop.enums.*;
import cloud.demand.app.modules.sop.enums.task.DwdTaskEnum;
import cloud.demand.app.modules.sop.service.task.dwd_task.DWDTaskService;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop.util.DataTransformUtil;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import cloud.demand.app.modules.sop.util.SopHttpIteratorUtil;
import cn.hutool.core.date.DateTime;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import yunti.boot.exception.BizException;

/**
 * cvm需求预测 DWD 接口
 */
@Service
@Slf4j
public class CvmDemandTask extends DWDTaskService {

    @Resource
    private DBHelper ckcldyuntiDBHelper;

    @Resource
    private DBHelper yuntidemandDBHelper;


    @Override
    public DwdTaskEnum task() {
        return DwdTaskEnum.CVM_NEW_DEMAND;
    }

    @SneakyThrows
    @Override
    @TaskLog(taskName = "sopCreateDWDData@CvmNewDemandTask")
    public void createDWDData(String version) {

        StopWatch stopWatch = new StopWatch("cvm需求预测");
        stopWatch.start("1. 情况ck版本分区数据");
        delete(version, DwdSopReportDemandCvmDO.class);
        stopWatch.stop();
        stopWatch.start("2. 请求上游api获取退回数据");

        LocalDate dateVersion = commonDbHelper.getStatTimeFromVersion(version);
        AtomicLong startId = new AtomicLong(CkDBUtils.startId());
        LocalDateTime statTime = DateUtils.toLocalDateTime(new Date());
        String startDate = getStartDate();
        LocalDate start = LocalDate.parse(startDate,DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 不从老王取数了
//        //已执行
//        doProcess(version,true,dateVersion,startId,statTime,startDate);
//        //未执行
//        doProcess(version,false,dateVersion,startId,statTime,startDate);
//        //未执行脏数据
//        doDirtyProcess(version,dateVersion,startId,statTime,startDate);

        // 从云梯获取数据
        doProcessFromYunti(version,dateVersion,startId,statTime,start);
    }

    public static void main(String[] args) {
        LocalDateTime dateTime = new DateTime(Long.parseLong("1724014813") * 1000).toLocalDateTime();
        System.out.println();
        System.out.println(dateTime.getHour());
    }

    private void doProcessFromYunti(String version, LocalDate dateVersion, AtomicLong startId, LocalDateTime statTime, LocalDate startDate) {
        LocalDate now = LocalDate.now();
        // 当天用实时表数据，历史用切片时间
        List<DwdYuntiCvmDemandForecastItemDfDO> yuntiData;
        LocalDateTime dateTime = SopDateUtils.verison2LocalDateTime(version);
        int hour = dateTime.getHour();
        // 当前且时间在9点后用实时表数据，否则用切片表
        if (now.equals(dateVersion) && hour >= 9) {
            yuntiData = queryYuntiRealTime(dateVersion,startDate);
        }else {
            yuntiData = queryYuntiSlice(dateVersion,startDate);
        }
        if (ListUtils.isEmpty(yuntiData)){
            throw new BizException(String.format("CVM需求云梯数据为空，切片日期：[%s]", DateUtils.formatDate(dateVersion)));
        }
        // ca数据
        Set<Long> caDemandIdSet;
        if (EnvUtils.isProduction()){
            caDemandIdSet = new HashSet<>(commonDbHelper.getCADemandIdList(dateVersion));
        }else {
            caDemandIdSet = new HashSet<>();
        }
        // 切片所在节假周年月
        String yearMonth = SopDateUtils.getYearMonthWithTable(dateVersion);
        // 已执行
        List<DwdSopReportDemandCvmDO> saveData = ListUtils.transform(
                yuntiData.stream().filter(item-> !SoeCommonUtils.isNullOrZone(item.getAppliedCoreAmount())).collect(Collectors.toList()),
                (item) -> transform(item, true ,caDemandIdSet,version, startId, statTime,yearMonth));
        // 未执行
        saveData.addAll(ListUtils.transform(
                yuntiData.stream().filter(item-> !SoeCommonUtils.isNullOrZone(item.getLaveCoreAmount())).collect(Collectors.toList()),
                (item) -> transform(item, false, null,version, startId, statTime,yearMonth)));
        // 写入数据
        insert(saveData);
    }

    private List<DwdYuntiCvmDemandForecastItemDfDO> queryYuntiSlice(LocalDate dateVersion, LocalDate startDate) {
        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        LocalDate plus = dateVersion.plus(-1, ChronoUnit.DAYS);
        String beforeDate = plus.format(DateTimeFormatter.ISO_LOCAL_DATE);

        // 切片日期
        whereContent.andEqual(DwdYuntiCvmDemandForecastItemDfDO::getStatDate, beforeDate);
        // 删除算力+运营部门
        whereContent.andNotIn(DwdYuntiCvmDemandForecastItemDfDO::getDeptName, Arrays.asList("算力平台","运营资源中心"));
        // 起始时间，默认从 2023 年开始
        whereContent.addAnd("use_time >= ?", startDate);
        // 排除0核心数据
        whereContent.addAnd("lave_core_amount > 0 or applied_core_amount > 0");

        return ORMUtils.db(ckcldyuntiDBHelper).getAll(DwdYuntiCvmDemandForecastItemDfDO.class, whereContent);
    }

    private List<DwdYuntiCvmDemandForecastItemDfDO> queryYuntiRealTime(LocalDate dateVersion, LocalDate startDate) {
        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        // 删除算力+运营部门
        whereContent.andNotIn(DwdYuntiCvmDemandForecastItemDfDO::getDeptName, Arrays.asList("算力平台","运营资源中心"));
        // 起始时间，默认从 2023 年开始
        whereContent.addAnd("use_time >= ?", startDate);
        // 排除0核心数据
        whereContent.addAnd("core_amount > 0 or applied_core_amount > 0");
        // 剔除删除的
        whereContent.addAnd("deleted = 0");

        List<YuntiDemandCvmItemDO> all = ORMUtils.db(yuntidemandDBHelper).getAll(YuntiDemandCvmItemDO.class, whereContent);
        return ListUtils.transform(all, this::transform);
    }

    public DwdYuntiCvmDemandForecastItemDfDO transform(YuntiDemandCvmItemDO item) {
        DwdYuntiCvmDemandForecastItemDfDO dwdYuntiCvmDemandForecastItemDfDO = new DwdYuntiCvmDemandForecastItemDfDO();
        dwdYuntiCvmDemandForecastItemDfDO.setId(item.getId());
        dwdYuntiCvmDemandForecastItemDfDO.setUseTime(item.getUseTime());
        dwdYuntiCvmDemandForecastItemDfDO.setPlanProductName(item.getPlanProductName());
        dwdYuntiCvmDemandForecastItemDfDO.setDeptName(item.getDeptName());
        dwdYuntiCvmDemandForecastItemDfDO.setBgName(item.getBgName());
        dwdYuntiCvmDemandForecastItemDfDO.setProjectName(item.getProjectName());
        dwdYuntiCvmDemandForecastItemDfDO.setCityName(item.getCityName());
        dwdYuntiCvmDemandForecastItemDfDO.setZoneName(item.getZoneName());
        dwdYuntiCvmDemandForecastItemDfDO.setResourcePoolType(toLong(item.getResourcePoolType()));
        dwdYuntiCvmDemandForecastItemDfDO.setCoreType(toLong(item.getCoreType()));
        dwdYuntiCvmDemandForecastItemDfDO.setInstanceFamily(item.getInstanceFamily());
        dwdYuntiCvmDemandForecastItemDfDO.setInstanceType(item.getInstanceType());
        dwdYuntiCvmDemandForecastItemDfDO.setInstanceModel(item.getInstanceModel());
        dwdYuntiCvmDemandForecastItemDfDO.setInstanceIo(toLong(item.getInstanceIo()));
        dwdYuntiCvmDemandForecastItemDfDO.setCbsResClassName(item.getCbsResClassName());
        dwdYuntiCvmDemandForecastItemDfDO.setLaveCvmAmount(item.getCvmAmount());
        dwdYuntiCvmDemandForecastItemDfDO.setLaveCoreAmount(new BigDecimal(item.getCoreAmount()));
        dwdYuntiCvmDemandForecastItemDfDO.setAppliedCvmAmount(item.getAppliedCvmAmount());
        dwdYuntiCvmDemandForecastItemDfDO.setAppliedCoreAmount(new BigDecimal(item.getAppliedCoreAmount()));
        dwdYuntiCvmDemandForecastItemDfDO.setGenerationType(item.getGenerationType());
        return dwdYuntiCvmDemandForecastItemDfDO;
    }

    private Long toLong(Integer v){
        return v == null ? 0L : v.longValue();
    }


    private void doDirtyProcess(String version,
                                LocalDate dateVersion,
                                AtomicLong startId,
                                LocalDateTime statTime,
                                String startDate) {
        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();

        // 取上一天的切片版本
        LocalDate plus = dateVersion.plus(-1, ChronoUnit.DAYS);
        String beforeDate = plus.format(DateTimeFormatter.ISO_LOCAL_DATE);

        // 切片日期
        whereContent.andEqual(DwdYuntiCvmDemandForecastItemDfDO::getStatDate, beforeDate);
        // 删除算力+运营部门
        whereContent.andNotIn(DwdYuntiCvmDemandForecastItemDfDO::getDeptName, Arrays.asList("算力平台","运营资源中心"));
        // 自研池
        whereContent.andEqual(DwdYuntiCvmDemandForecastItemDfDO::getResourcePoolType,0);
        // 脏数据格式
        whereContent.addAnd("instance_model like '%c%g'");
        // 起始时间
        whereContent.addAnd("use_time >= ?", SopDateUtils.getMonthFirstDay(SopDateUtils.dateVersion(dateVersion)));
        // 排除0核心数据
        whereContent.addAnd("lave_core_amount > 0");

        List<DwdYuntiCvmDemandForecastItemDfDO> raw = ORMUtils.db(ckcldyuntiDBHelper).getAll(DwdYuntiCvmDemandForecastItemDfDO.class, whereContent);
        String yearMonth = SopDateUtils.getYearMonthWithTable(dateVersion);
        List<DwdSopReportDemandCvmDO> saveData = ListUtils.transform(raw, (item) -> transform(item, false, null,version, startId, statTime,yearMonth));

        insert(saveData);
    }


    private void doProcess(String version,
                           boolean isExecuted,
                           LocalDate indexDate,
                           AtomicLong startId,
                           LocalDateTime statTime,
                           String startDate) {
        int current = 0;
        int size = getDefSize();

        String dateVersion = SopDateUtils.dateVersion(indexDate);

        SopServerPageReq pageReq = SopServerPageReq.builder()
                .version(version)
                .pageNum(current)
                .pageSize(size)
                .startDate(startDate)
                .dateVersion(dateVersion)
                .dataType(isExecuted ? ExecutedType.EXECUTED.getCode() : ExecutedType.NOT_EXECUTED.getCode())
                .build();

        SopPageRes<SopCvmDemandResList> firstRes = sopService.getCvmDemandByVersion(pageReq);
        if (firstRes == null || CollectionUtils.isEmpty(firstRes.getBody())) {
            return;
        }

        // 初始化本地清洗映射结合
        initLocalMap(firstRes.getBody());
        List<DwdSopReportDemandCvmDO> saveData = getSaveData(version,isExecuted,indexDate,startId,statTime,firstRes.getBody());
        insert(saveData);

        if (firstRes.getTotal() <= size) {
            return;
        }
        current++;
        long total = firstRes.getTotal() - size;

        SopHttpIteratorUtil<SopCvmDemandResList> iterator = new SopHttpIteratorUtil<>(size, current, total,
                (req) -> sopService.getCvmDemandByVersion(SopServerPageReq.builder()
                        .version(version)
                        .pageNum(req.getCurrent())
                        .pageSize(req.getSize())
                        .startDate(startDate)
                        .dataType(isExecuted ? ExecutedType.EXECUTED.getCode() : ExecutedType.NOT_EXECUTED.getCode())
                        .dateVersion(dateVersion)
                        .build()));

        while (iterator.hasNext()) {
            List<SopCvmDemandResList> body = iterator.next();
            if (!CollectionUtils.isEmpty(body)) {
                // 初始化本地清洗映射结合(可追加)
                initLocalMap(body);
                saveData = getSaveData(version,isExecuted,indexDate,startId,statTime,body);
                insert(saveData);
            }
        }

    }

    private List<DwdSopReportDemandCvmDO> getSaveData(String version,
                                                      boolean isExecuted,
                                                      LocalDate indexDate,
                                                      AtomicLong startId,
                                                      LocalDateTime statTime,
                                                      List<SopCvmDemandResList> body){
        return body.stream()
                .filter(getFilter(isExecuted))
                .map((item) -> transform(item,  version, isExecuted,indexDate, startId, statTime))
                .collect(Collectors.toList());
    }

    /**
     * 以下不计算在内（2，3在dwd2dws.sql中处理）
     * 1. 执行核心数小于等于0
     * 2. 部门为运营资源中心（32）或者算力平台（1129）
     * 3. 实例规模为空
     **/
    private Predicate<SopCvmDemandResList> getFilter(boolean isExecuted){
        return (item)->{
            return !isExecuted || DataTransformUtil.isPositive(item.getExeAmount());
//            if (Objects.equals(item.getDeptName(),"运营资源中心") || Objects.equals(item.getDeptName(),"算力平台")){
//                return false;
//            }
//            return !StringUtils.isBlank(item.getInstanceModel());
        };
    }

    private DwdSopReportDemandCvmDO transform(DwdYuntiCvmDemandForecastItemDfDO data,
                                              boolean isExecuted,
                                              Set<Long> caDemandIdSet,
                                              String version,
                                              AtomicLong startId,
                                              LocalDateTime statTime,
                                              String yearMonth){
        DwdSopReportDemandCvmDO resultDO = new DwdSopReportDemandCvmDO();
        resultDO.setVersion(version);
        resultDO.setId(startId.getAndIncrement());
        resultDO.setIsDirty(FlagType.NO.getCode());
        resultDO.setStatTime(statTime);
        resultDO.setDeptName(data.getDeptName());

        // 但族不为空且类型为空时(只考虑磁盘的情况)
        if (StringUtils.isNotBlank(data.getInstanceType()) && StringUtils.isBlank(data.getInstanceModel())){
            BigDecimal capacity = (BigDecimal) DataTransformUtil.getT(data, null, new IGetTType[]{
                    new IGetTType<SopCvmDemandResList, BigDecimal>() {
                        @Override
                        public Predicate<SopCvmDemandResList> getCheckFun() {
                            return (item) -> item.getInstanceType().contains("高IO") || item.getInstanceType().contains("大数据");
                        }
                        @Override
                        public Function<SopCvmDemandResList, BigDecimal> getReturnFun() {
                            return (item) -> data.getLaveAllDiskAmount();
                        }
                    }
            });
            resultDO.setCapacity(capacity);
        }
        resultDO.setBgName(data.getBgName());
//        resultDO.setBusinessType(data.getBusinessType());
//        resultDO.setObsBusinessType(data.getBusinessType());
        SopDateUtils.dateTransform(() -> LocalDate.parse(data.getUseTime(), DateTimeFormatter.ISO_DATE), resultDO);
        resultDO.setResType(ResourceType.CVM.getName());
        resultDO.setResPoolType(SopResourcePool.getDesc(data.getResourcePoolType()+""));
        resultDO.setObsProjectType(data.getProjectName());
        resultDO.setPlanProductName(data.getPlanProductName());
//        resultDO.setCustomhouseTitle(data.getAreaType());
//        resultDO.setCountryName(data.getCountry());
        resultDO.setCityName(data.getCityName());
        resultDO.setCmdbCampusName(data.getDefaultCampus());
//        resultDO.setCmdbModuleName(data.getModule());
        resultDO.setCvmGinsKingdom(data.getInstanceFamily());
        resultDO.setCvmGinsType(data.getInstanceModel());
        resultDO.setCvmGinsFamily(data.getInstanceType());


        // 设置isCa
        if (isExecuted){
            resultDO.setIsCa(FlagType.getCode(ListUtils.isNotEmpty(caDemandIdSet) && caDemandIdSet.contains(data.getId())));
            resultDO.setIsExecuted(FlagType.YES.getCode());
            // 已执行不参与对冲
            resultDO.setIsHedge(FlagType.NO.getCode());
            resultDO.setHasHedged(FlagType.NO.getCode());

            // 已执行取 applied
            resultDO.setNum(data.getAppliedCvmAmount());
            resultDO.setCoreNum(data.getAppliedCoreAmount());
        }else {
            // 未执行只有自研并且未过期的参与对冲
            String resPoolType = resultDO.getResPoolType();
            if (Objects.equals(SopResourcePool.SELF.getDesc(),resPoolType)){
                String holidayYearMonth = SopDateUtils.getYearMonthWithTable(resultDO.getIndexDate());
                // 未过期才是有效对冲
                if (yearMonth.compareTo(holidayYearMonth) <= 0){
                    resultDO.setIsHedge(FlagType.YES.getCode());
                    resultDO.setHasHedged(FlagType.YES.getCode());
                }else {
                    resultDO.setIsHedge(FlagType.NO.getCode());
                    resultDO.setHasHedged(FlagType.NO.getCode());
                }
            }else {
                resultDO.setIsHedge(FlagType.NO.getCode());
                resultDO.setHasHedged(FlagType.NO.getCode());
            }

            // 未执行不参与ca
            resultDO.setIsCa(FlagType.NO.getCode());
            resultDO.setIsExecuted(FlagType.NO.getCode());

            // 未执行取 lave
            resultDO.setNum(data.getLaveCvmAmount());
            resultDO.setCoreNum(data.getLaveCoreAmount());
        }
        resultDO.setJsonText(null);

        // 以下字段没有值，使用默认值
        resultDO.setTxyZoneName(data.getZoneName());

        // clean以下字段： CoreNum  Capacity  CvmGinsFamily  BgName  CustomBgName  DeptName
        cleaning(resultDO);


        return resultDO;
    }



    private DwdSopReportDemandCvmDO transform(SopCvmDemandResList data,
                                              String version,
                                              boolean isExec,
                                              LocalDate indexDate,
                                              AtomicLong startId,
                                              LocalDateTime statTime) {
        DwdSopReportDemandCvmDO resultDO = new DwdSopReportDemandCvmDO();
        resultDO.setVersion(version);
        resultDO.setId(startId.getAndIncrement());
        resultDO.setStatTime(statTime);
        resultDO.setDeptName(data.getDeptName());
        resultDO.setNum(ObjectUtils.defaultIfNull(isExec ? data.getExeAmount() : data.getNonAmount() ,BigDecimal.ZERO));
        resultDO.setCoreNum(ObjectUtils.defaultIfNull(isExec ? data.getCpuExeAmount() : data.getCpuNonAmount(), BigDecimal.ZERO));
        // 但族不为空且类型为空时(只考虑磁盘的情况)
        if (StringUtils.isNotBlank(data.getInstanceType()) && StringUtils.isBlank(data.getInstanceModel())){
            BigDecimal capacity = (BigDecimal) DataTransformUtil.getT(data, null, new IGetTType[]{
                    new IGetTType<SopCvmDemandResList, BigDecimal>() {
                        @Override
                        public Predicate<SopCvmDemandResList> getCheckFun() {
                            return (item) -> item.getInstanceType().contains("高IO") || item.getInstanceType().contains("大数据");
                        }
                        @Override
                        public Function<SopCvmDemandResList, BigDecimal> getReturnFun() {
                            return (item) -> {
                                Integer disk = isExec ? item.getDiskExeAmount() : item.getDiskNonAmount();
                                return disk == null ? null : BigDecimal.valueOf(disk);
                            };
                        }
                    }
            });
            resultDO.setCapacity(capacity);
        }
        resultDO.setBgName(data.getBgName());
        resultDO.setBusinessType(data.getBusinessType());
        resultDO.setObsBusinessType(data.getBusinessType());
        SopDateUtils.dateTransform(data,resultDO);
        resultDO.setResType(data.getResourceType());
        resultDO.setResPoolType(SopResourcePool.getDesc(data.getResourcePool()));
        resultDO.setObsProjectType(data.getProjectType());
        resultDO.setPlanProductName(data.getPlanProductName());
        resultDO.setCustomhouseTitle(data.getAreaType());
        resultDO.setCountryName(data.getCountry());
        resultDO.setCityName(data.getCity());
        resultDO.setCmdbCampusName(data.getCampus());
        resultDO.setCmdbModuleName(data.getModule());
        resultDO.setCvmGinsKingdom(data.getInstanceFamily());
        resultDO.setCvmGinsType(data.getInstanceModel());
        resultDO.setCvmGinsFamily(data.getInstanceType());
        resultDO.setIsHedge(data.getIsHedging());
        resultDO.setHasHedged(data.getIsValidHedging());

        // 设置isCa
        resultDO.setIsCa(FlagType.getCode(Objects.equals(data.getIsCa(),1)));
        resultDO.setJsonText(null);

        //判断是否执行
        if (isExec) {
            resultDO.setIsExecuted(FlagType.YES.getCode());
        } else {
            resultDO.setIsExecuted(FlagType.NO.getCode());
        }

        // clean以下字段： CoreNum  Capacity  CvmGinsFamily  BgName  CustomBgName  DeptName
        cleaning(resultDO);

        // 以下字段没有值，使用默认值
        resultDO.setTxyZoneName(data.getZoneName());

        return resultDO;
    }
}
