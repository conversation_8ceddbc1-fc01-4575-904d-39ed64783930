package cloud.demand.app.modules.rrp_remake.service.impl;

import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.ValidateResultItemDTO;
import cloud.demand.app.modules.p2p.product_demand.service.ProductDemandDictService;
import cloud.demand.app.modules.p2p.product_demand.service.impl.ProductDemandDictServiceImpl;
import cloud.demand.app.modules.rrp_new_feature.service.AutoDeliveryDemandForecastService;
import cloud.demand.app.modules.rrp_new_feature.service.RealTimeDemandService;
import cloud.demand.app.modules.rrp_remake.entity.FlowInfoDO;
import cloud.demand.app.modules.rrp_remake.entity.ProductInfoDO;
import cloud.demand.app.modules.rrp_remake.entity.ProductInfoLogicDeletedDO;
import cloud.demand.app.modules.rrp_remake.entity.RrpConfigDO;
import cloud.demand.app.modules.rrp_remake.enums.AuthRoleEnum;
import cloud.demand.app.modules.rrp_remake.enums.DemandFlagEnum;
import cloud.demand.app.modules.rrp_remake.enums.DemandFlowStepEnum;
import cloud.demand.app.modules.rrp_remake.enums.DemandImportSourceEnum;
import cloud.demand.app.modules.rrp_remake.enums.DemandModelEnum;
import cloud.demand.app.modules.rrp_remake.enums.DemandTypeEnum;
import cloud.demand.app.modules.rrp_remake.enums.RrpConfigStatusEnum;
import cloud.demand.app.modules.rrp_remake.model.AmountWithCurplanDTO;
import cloud.demand.app.modules.rrp_remake.model.ExcelDataGroup;
import cloud.demand.app.modules.rrp_remake.model.ProductInfoSaveItemDTO;
import cloud.demand.app.modules.rrp_remake.model.SaveProductInfoValidateResultDTO;
import cloud.demand.app.modules.rrp_remake.model.YearMonth;
import cloud.demand.app.modules.rrp_remake.model.YearMonthNumDTO;
import cloud.demand.app.modules.rrp_remake.service.DemandImportService;
import cloud.demand.app.modules.rrp_remake.service.DictService;
import cloud.demand.app.modules.rrp_remake.service.MyAuthService;
import cloud.demand.app.modules.rrp_remake.service.VersionService;
import cloud.demand.app.modules.rrp_remake.util.RrpExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.google.common.collect.ImmutableSet;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringJoiner;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@Slf4j
public class DemandImportServiceImpl implements DemandImportService {

    @Resource(name = "rrpRemakeDictServiceImpl")
    DictService dictService;
    @Resource
    ProductDemandDictService productDemandDictService;
    @Resource
    MyAuthService myAuthService;
    @Resource
    VersionService versionService;
    @Resource
    RealTimeDemandService realTimeDemandService;
    @Resource
    AutoDeliveryDemandForecastService autoDeliveryDemandForecastService;
    @Resource
    DBHelper rrpDBHelper;

    /**
     * 给出简单样例数据
     *
     * @param ymSize 年月的个数
     * @return 示例数据
     */
    private List<Object> givenSampleData(int ymSize) {
        String[] id = new String[]{"id1", "id2"};
        String[] demandType = new String[]{"内部规划", "客户需求"};
        String[] obsProjectType = new String[]{"常规项目", "常规项目"};
        String[] modBusinessTypeName = new String[]{"公有云-通用", "公有云-黑石租赁"};
        String[] projSetName = new String[]{"自研上云", "非自研上云"};
        String[] region = new String[]{"华东", "华南"};
        String[] zone = new String[]{"上海", "广州"};
        String[] campus = new String[]{"上海-宝信", "广州-华新园"};
        String[] industry = new String[]{"常规", "云产品其它"};
        String[] customerName = new String[]{"客户1", "客户2"};
        String[] deviceType = new String[]{"Y0-MS52-25G", "Y0-MI52-25G"};
        String[] remark = new String[]{"备注1", "备注2"};
        String[] reason = new String[]{"功能优化", "客户突发"};
        List<Object> data = new LinkedList<>();
        for (int i = 0; i < 2; i++) {
            Map<String, Object> m = MapUtils.of(
                    "id", id[i],
                    "demandType", demandType[i],
                    "obsProjectType", obsProjectType[i],
                    "modBusinessTypeName", modBusinessTypeName[i],
                    "projSetName", projSetName[i],
                    "region", region[i],
                    "zone", zone[i],
                    "campus", campus[i],
                    "industry", industry[i],
                    "customerName", customerName[i],
                    "deviceType", deviceType[i],
                    "remark", remark[i],
                    "reason", reason[i]);
            for (int j = 1; j <= ymSize; j++) {
                m.put("m" + j, 10);
                m.put("n" + j, 10);
            }
            data.add(m);
        }
        return data;
    }

    private FileNameAndBytesDTO excelMaker(List itemDTOS, List<YearMonth> yearMonthList, String flag, String model,
                                           String fileName) {

        // 横着写的 config
        FillConfig fillConfig = FillConfig.builder().direction(WriteDirectionEnum.HORIZONTAL).build();
        String templatePath = RrpExcelUtil.getTemplatePath(flag, model);
        InputStream templateIn = IOUtils.readClasspathResourceInputStream(templatePath);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(out)
                .withTemplate(templateIn).build();

        // 构建年月到周次的映射
        Map<YearMonth, List<Integer>> yearMonth2WeeksList = dictService.batchGetHolidayWeekByYearMonth(yearMonthList);

        List<Map<String, Object>> weekYm = RrpExcelUtil.buildYearMonthWeekMap(yearMonthList, yearMonth2WeeksList, flag);
        List<Map<String, Object>> monthYm = RrpExcelUtil.buildYearMonthMap(yearMonthList, flag);

        DemandImportSourceEnum[] sheetEnums = DemandImportSourceEnum.values();
        for (DemandImportSourceEnum e : sheetEnums) {
            if (DemandImportSourceEnum.AUTO_EXTEND.equals(e)) {
                continue;
            }
            // 写到对应sheet
            WriteSheet writeSheet = EasyExcel.writerSheet(e.getExcelSheetNo()).build();
            if (DemandModelEnum.MONTH.getKey().equals(model) && !DemandImportSourceEnum.FROM_PPL.equals(e)) {
                excelWriter.fill(new FillWrapper("ym", monthYm), fillConfig, writeSheet);
            } else {
                excelWriter.fill(new FillWrapper("ym", weekYm), fillConfig, writeSheet);
            }
            // 两行示例数据
            excelWriter.fill(new FillWrapper("item", itemDTOS), writeSheet);
        }
        excelWriter.finish();
        FileNameAndBytesDTO fileNameAndBytesDTO = new FileNameAndBytesDTO();
        fileNameAndBytesDTO.setBytes(out.toByteArray());
        fileNameAndBytesDTO.setFileName(fileName);
        return fileNameAndBytesDTO;
    }

    @Override
    public FileNameAndBytesDTO downloadExcelTemplate(String flag, String model) {

        RrpConfigDO currentRrpConfig = versionService.getCurrentRrpConfig();
        if (currentRrpConfig == null) {
            throw new BizException("当前没有开放的版本，没有可导出的模版");
        }
        List<YearMonth> yearMonthList = currentRrpConfig.demandMonthList();
        return excelMaker(givenSampleData(yearMonthList.size()), yearMonthList, flag, model, "产品需求录入模板.xlsx");
    }

    @Override
    public ExcelDataGroup decodeExcel(MultipartFile multipartFile, Integer versionId, String planProduct, String flag, String model) {
        ExcelDataGroup resp = new ExcelDataGroup();
        resp.setProductInput(decodeExcelToItem(multipartFile, versionId, flag, model,
                DemandImportSourceEnum.PRODUCT_INPUT.getExcelSheetNo()));
        // 只有"腾讯云-CVM"支持PPL sheet
        if (ProductTypeEnum.CVM.getName().equals(planProduct)) {
            // ppl导入的仅支持week模式
            resp.setFromPpl(decodeExcelToItem(multipartFile, versionId, flag, DemandModelEnum.WEEK.getKey(),
                    DemandImportSourceEnum.FROM_PPL.getExcelSheetNo()));
        }
        resp.setSystemGenerate(decodeExcelToItem(multipartFile, versionId, flag, model,
                DemandImportSourceEnum.SYSTEM_GENERATE.getExcelSheetNo()));
        return resp;
    }


    private void doCheckTemplate(MultipartFile multipartFile, List<YearMonth> yearMonthList,
                                 Map<YearMonth, List<Integer>> yearMonth2WeeksList, String flag, String model, int sheetNo) throws IOException {

        List<Map<String, Object>> template;
        if (DemandModelEnum.MONTH.getKey().equals(model)) {
            template = RrpExcelUtil.buildYearMonthMap(yearMonthList, flag);
        } else {
            template = RrpExcelUtil.buildYearMonthWeekMap(yearMonthList, yearMonth2WeeksList, flag);
        }

        EasyExcel.read(multipartFile.getInputStream(),
                new AnalysisEventListener<Map<Integer, String>>() {
                    @Override
                    public void invoke(Map<Integer, String> data, AnalysisContext context) {
                        if (context.readRowHolder().getRowIndex() == 2) { // 索引从0开始，第三行的索引为2

                            Iterator<Map<String, Object>> templateIterator = template.iterator();

                            // 获取模版中 配置开始的下标
                            int begin = DemandImportSourceEnum.SYSTEM_GENERATE.getExcelSheetNo() == sheetNo ? 13 : 12;

                            // 拿到了第三行数据，即模版配置
                            for (int i = begin; i < data.size(); i++) {

                                String ym = data.get(i);
                                if (ym == null) {
                                    break;
                                }
                                if (templateIterator.hasNext()) {
                                    Map<String, Object> next = templateIterator.next();
                                    if (DemandModelEnum.MONTH.getKey().equals(model)) {
                                        if (!ym.equals(next.get("yearMonth"))) {
                                            throw new BizException(String.format("sheet页:[%s]与[%s(%s)]标准模版配置不一致，请勿混用模版",
                                                    DemandImportSourceEnum.getChByExcelSheetNo(sheetNo),
                                                    DemandFlagEnum.getChByKey(flag),
                                                    DemandModelEnum.getChByKey(model)));
                                        }
                                    } else {
                                        if (!ym.equals(next.get("yearMonth") + "\n" + next.get("week"))) {
                                            throw new BizException(String.format("sheet页:[%s]与[%s(%s)]标准模版配置不一致，请勿混用模版",
                                                    DemandImportSourceEnum.getChByExcelSheetNo(sheetNo),
                                                    DemandFlagEnum.getChByKey(flag),
                                                    DemandModelEnum.getChByKey(model)));
                                        }
                                    }
                                } else {
                                    throw new BizException(String.format("sheet页:[%s]与[%s(%s)]标准模版配置不一致，请勿混用模版",
                                            DemandImportSourceEnum.getChByExcelSheetNo(sheetNo),
                                            DemandFlagEnum.getChByKey(flag),
                                            DemandModelEnum.getChByKey(model)));
                                }
                            }
                        }
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                    }
                }).sheet(sheetNo).doRead();
    }


    // 拆分月模式需求到周
    // 传入week的列表，以及需要拆分的数值，那么将尽可能的均分
    public Map<Integer, Integer> distributeValues(List<Integer> weeks, int num) {
        int size = weeks.size();
        int baseValue = num / size;
        int remainder = Math.abs(num % size);

        return IntStream.range(0, size)
                .boxed()
                .collect(Collectors.toMap(weeks::get, i -> baseValue + (i < remainder ? (num < 0 ? -1 : 1) : 0)));
    }

    private List<Map<String, Object>> decodeExcelToItem(MultipartFile multipartFile,
                                                        Integer versionId, String flag, String model, int sheetNo) {

        Map<String, String> reasonMap = productDemandDictService.listAllReasonMap();

        List<Map<String, Object>> resultList = new ArrayList<>();
        try {
            RrpConfigDO rrpConfigDO = rrpDBHelper.getOne(RrpConfigDO.class,
                    "where id = ?", versionId);
            List<YearMonth> yearMonthList = rrpConfigDO.demandMonthList();
            Map<YearMonth, List<Integer>> yearMonth2WeekList = null;
            if (rrpConfigDO != null) {
                yearMonth2WeekList = dictService.batchGetHolidayWeekByYearMonth(yearMonthList);
            }
            if (CollectionUtils.isEmpty(yearMonth2WeekList.keySet())) {
                throw new BizException("获取当前版本滚动配置失败");
            }
            List<YearMonth> finalYearMonthList = yearMonthList;
            Map<YearMonth, List<Integer>> finalYearMonth2WeekList = yearMonth2WeekList;

            // 读数据之前 应该先探测模版是否正确
            doCheckTemplate(multipartFile, finalYearMonthList, finalYearMonth2WeekList, flag, model, sheetNo);

            EasyExcel.read(multipartFile.getInputStream(),
                    new AnalysisEventListener<Map<Integer, String>>() {
                        @Override
                        public void invoke(Map<Integer, String> data, AnalysisContext context) {
                            if (CollectionUtils.isEmpty(data)
                                    || CollectionUtils.isEmpty(data.values()
                                    .stream()
                                    .filter(e -> !StringUtils.isBlank(e))
                                    .collect(Collectors.toSet()))) {
                                // 全空的，代表遇到行尾了，结束
                                return;
                            }
                            Map<String, Object> dataMap = new LinkedHashMap<>();
                            int curReadCol = 0;
                            if (sheetNo == DemandImportSourceEnum.SYSTEM_GENERATE.getExcelSheetNo()) {
                                // 系统补全有一列系统补的
                                dataMap.put("dbId", data.get(curReadCol++));
                            }
                            // 读固定的业务信息
                            dataMap.put("demandType", data.get(curReadCol++));
                            dataMap.put("obsProjectType", data.get(curReadCol++));
                            dataMap.put("modBusinessTypeName", data.get(curReadCol++));
                            dataMap.put("projSetName", data.get(curReadCol++));
                            dataMap.put("region", data.get(curReadCol++));
                            dataMap.put("zone", data.get(curReadCol++));
                            dataMap.put("campus", data.get(curReadCol++));
                            dataMap.put("industry", data.get(curReadCol++));
                            dataMap.put("customerName", data.get(curReadCol++));
                            dataMap.put("deviceType", data.get(curReadCol++));
                            dataMap.put("productRemark", data.get(curReadCol++));
                            dataMap.put("reason", data.get(curReadCol++));
                            dataMap.put("reasonType", reasonMap.get(dataMap.get("reason")));
                            // 读动态的年月预测量信息
                            if (DemandModelEnum.MONTH.getKey().equals(model)) {
                                // 月份模式
                                for (YearMonth yearMonth : finalYearMonthList) {
                                    if (flag.equals(DemandFlagEnum.RES.getKey())) {
                                        // 资源组审批 读取录入值和资源组调整值
                                        Integer amount = NumberUtils.parseInt(data.get(curReadCol++));
                                        Integer curplanAmount = NumberUtils.parseInt(data.get(curReadCol++));
                                        if (yearMonth.getOpt()) {
                                            // 必填月份 没填给0
                                            amount = amount == null ? 0 : amount;
                                            curplanAmount = curplanAmount == null ? 0 : curplanAmount;
                                        }
                                        if (amount != null) {
                                            // 选填有预测值，资源组调整值为空就给0
                                            curplanAmount = curplanAmount == null ? 0 : curplanAmount;
                                            // 拆分成周返回
                                            // 获取当前月份的所有周次
                                            List<Integer> weeks = finalYearMonth2WeekList.get(yearMonth);
                                            Map<Integer, Integer> amountByWeeks = distributeValues(weeks, amount);
                                            Map<Integer, Integer> curplanAmountByWeeks = distributeValues(weeks,
                                                    curplanAmount);
                                            for (Integer week : weeks) {
                                                dataMap.put(yearMonth.getYearMonth() + "-" + week,
                                                        new AmountWithCurplanDTO(amountByWeeks.get(week),
                                                                curplanAmountByWeeks.get(week)));
                                            }
                                        }
                                    } else {
                                        // 产品录入 只有录入值
                                        Integer amount = NumberUtils.parseInt(data.get(curReadCol++));
                                        if (yearMonth.getOpt()) {
                                            // 必填月份 没填给0
                                            amount = amount == null ? 0 : amount;
                                        }
                                        if (amount != null) {
                                            // 拆分成周返回
                                            // 获取当前月份的所有周次
                                            List<Integer> weeks = finalYearMonth2WeekList.get(yearMonth);
                                            Map<Integer, Integer> amountByWeeks = distributeValues(weeks, amount);
                                            for (Integer week : weeks) {
                                                dataMap.put(yearMonth.getYearMonth() + "-" + week,
                                                        new AmountWithCurplanDTO(amountByWeeks.get(week), 0));
                                            }
                                        }
                                    }
                                }
                            } else {
                                // 周次模式
                                for (YearMonth yearMonth : finalYearMonthList) {
                                    for (Integer week : finalYearMonth2WeekList.get(yearMonth)) {
                                        if (flag.equals(DemandFlagEnum.RES.getKey())) {
                                            // 资源组审批 读取录入值和资源组调整值
                                            Integer amount = NumberUtils.parseInt(data.get(curReadCol++));
                                            Integer curplanAmount = NumberUtils.parseInt(data.get(curReadCol++));
                                            if (yearMonth.getOpt()) {
                                                // 必填月份 没填给0
                                                amount = amount == null ? 0 : amount;
                                                curplanAmount = curplanAmount == null ? 0 : curplanAmount;
                                            }
                                            if (amount != null) {
                                                // 选填有预测值，资源组调整值为空就给0
                                                curplanAmount = curplanAmount == null ? 0 : curplanAmount;
                                                dataMap.put(yearMonth.getYearMonth() + "-" + week,
                                                        new AmountWithCurplanDTO(amount, curplanAmount));
                                            }
                                        } else {
                                            // 产品录入 只有录入值
                                            Integer amount = NumberUtils.parseInt(data.get(curReadCol++));
                                            if (yearMonth.getOpt()) {
                                                // 必填月份 没填给0
                                                amount = amount == null ? 0 : amount;
                                            }
                                            if (amount != null) {
                                                dataMap.put(yearMonth.getYearMonth() + "-" + week,
                                                        new AmountWithCurplanDTO(amount, 0));
                                            }
                                        }
                                    }
                                }
                            }
                            resultList.add(dataMap);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext context) {
                        }
                    }).sheet(sheetNo).headRowNumber(3).doRead();
        } catch (Exception e) {
            log.error("13周产品需求预测解析EXCEL 文件失败:", e);
            throw new BizException("文件解析失败:" + e.getMessage());
        }
        return resultList;
    }

    @Override
    @Transactional("rrpTransactionManager")
    @Synchronized(namespace = "CHANGE-RRP-PRODUCT-INFO", waitLockMillisecond = 100, keyScript = "args[1]", throwExceptionIfNotGetLock = false)
    public Object saveItem(RrpConfigDO rrpConfigDO, String product, List<ProductInfoSaveItemDTO> data,
                           boolean needSubmit) {
        String username = LoginUtils.getUserName();

        // 是否当前产品的资源组成员
        boolean isCOMD = myAuthService.listMyPlanProduct(username, AuthRoleEnum.COMD_APPROVER).contains(product);

        // 被拒绝的状态
        List<Integer> rejectStatus = Arrays.asList(DemandFlowStepEnum.REJECT_ID_ARRAY);
        // 审批中的状态
        List<Integer> approvingStatus = Arrays.asList(DemandFlowStepEnum.APPROVE_ID_ARRAY);

        // 错误接受者
        SaveProductInfoValidateResultDTO validateResultDTO = new SaveProductInfoValidateResultDTO();

        if (rrpConfigDO.getStatus().equals(RrpConfigStatusEnum.CLOSE.getCode())) {
            validateResultDTO.appendGlobalError("当前版本[" + rrpConfigDO.getPlanVersion() + "]已关闭，无法保存");
        }

        // 校验时间是否是在当前版本开放时间
        FlowInfoDO flowInfoDO = rrpDBHelper.getOne(FlowInfoDO.class,
                "where rrp_config_id = ? and product = ?", rrpConfigDO.getId(), product);
        LocalDate now = DateUtils.toLocalDate(new Date());
        if (rrpConfigDO.getStartTime().isAfter(now)
                || rrpConfigDO.getEndTime().isBefore(now)) {
            // 此时说明不在版本开放时间了，没有提交过的不允许提交了
            // 但是提交过被驳回的，版本关闭之前依旧支持修改后提交
            if (!(flowInfoDO != null && rejectStatus.contains(flowInfoDO.getCurrentStep()) || isCOMD)) {
                validateResultDTO.appendGlobalError("当前版本[" + rrpConfigDO.getPlanVersion() + "]预测录入时间已结束");
            }
        }

        boolean isApproving = flowInfoDO != null && approvingStatus.contains(flowInfoDO.getCurrentStep());

        if (isApproving) {
            // 产品调整，资源组调整权限控制
            if (!isCOMD) {
                validateResultDTO.appendGlobalError("当前规划产品[" + product + "]已经在审批中，您没有资源组调整的权限");
            }
        } else {
            // 录入权限控制
            Set<String> planProductSet = myAuthService.listMyPlanProduct(username,
                    ImmutableSet.of(AuthRoleEnum.PRODUCT_SUBMITER));
            if (!planProductSet.contains(product) && !isCOMD) {
                validateResultDTO.appendGlobalError(
                        "您没有当前规划产品[" + product + "]的录入权限，请联系管理员添加权限");
            }
        }

        if (!CollectionUtils.isEmpty(data)) {
            // 局部校验
            int rowId = 1;
            Set<String> projSetNameSet = dictService.listAllProjSetName();
            Map<String, String> reasonMap = productDemandDictService.listAllReasonMap();
            Map<String, List<String>> campus2ModBizType = productDemandDictService.buildCampus2ModuleBusinessTypeNameMap();
            Set<String> reasonSet = new HashSet<>(reasonMap.keySet());
            List<String> regionSet = productDemandDictService.listAllRegionName();
            Map<String, List<Integer>> obsMap = dictService.buildObsProjectType2DemandYearByPlanProduct(product);
            Map<String, String> zoneRegionMap = productDemandDictService.listAllZoneData(null)
                    .stream()
                    .collect(Collectors.toMap(ProductDemandDictServiceImpl.BaseDictData::getName,
                            ProductDemandDictServiceImpl.ZoneBaseDictData::getRegionName,
                            (e1, e2) -> e1));
            List<ProductDemandDictServiceImpl.FullCampusBaseDictData> fullCampusBaseDictData = productDemandDictService.listAllCampusData(
                    null);
            Map<String, String> campusZoneMap = ListUtils.toMap(fullCampusBaseDictData,
                    ProductDemandDictServiceImpl.BaseDictData::getName,
                    ProductDemandDictServiceImpl.FullCampusBaseDictData::getZoneName);
            Set<String> deviceTypeSet = productDemandDictService.listAllDeviceTypeName(product);
            for (ProductInfoSaveItemDTO dto : data) {
                dto.setRowId(rowId);
                ValidateResultItemDTO validateResultItemDTO = null;
                StringJoiner sj = new StringJoiner(",");
                dto.getYearMonthValueList().forEach(e -> {
                    if (e.getNum() == null) {
                        e.setNum(0);
                    }
                    if (e.getCurplanAdjust() == null) {
                        e.setCurplanAdjust(0);
                    }
                    if (e.getNum() + e.getCurplanAdjust() < 0) {
                        sj.add(e.getChineseName());
                    }
                });
                if (!StringUtils.isBlank(sj.toString())) {
                    validateResultItemDTO = appendItemError("以下时间的需求量干预后是负数：" + sj,
                            validateResultItemDTO);
                }
                // region，非空，在字典内
                validateResultItemDTO = assertNotBlank("Region", dto.getRegion(), validateResultItemDTO);
                validateResultItemDTO = assertInDict("Region", dto.getRegion(), regionSet,
                        validateResultItemDTO);
                if ("待定".equals(dto.getRegion())) {
                    validateResultItemDTO = appendItemError("您选择的region【待定】所属国家含义不清，请选择需求目标国家下有意义的region",
                            validateResultItemDTO);
                }
                // zone，非空，在字典内，和region对应的上
                validateResultItemDTO = assertNotBlank("Zone", dto.getZone(), validateResultItemDTO);
                validateResultItemDTO = assertInDict("Zone", dto.getZone(), zoneRegionMap.keySet(),
                        validateResultItemDTO);
                if (!StringUtils.isBlank(dto.getZone()) && !StringUtils.isBlank(
                        dto.getRegion())) {
                    String matchRegion = zoneRegionMap.get(dto.getZone());
                    if (matchRegion != null && !dto.getRegion().equals(matchRegion)) {
                        validateResultItemDTO = appendItemError("Zone所属的Region应为[" + matchRegion + "]",
                                validateResultItemDTO);
                    }
                }
                // campus，非空，在字典内，和campus对应的上
                validateResultItemDTO = assertNotBlank("Campus", dto.getCampus(), validateResultItemDTO);
                validateResultItemDTO = assertInDict("Campus", dto.getCampus(), campusZoneMap.keySet(),
                        validateResultItemDTO);
                if (!StringUtils.isBlank(dto.getCampus()) && !StringUtils.isBlank(
                        dto.getZone())) {
                    String matchZone = campusZoneMap.get(dto.getCampus());
                    if (matchZone != null && !dto.getZone().equals(matchZone)) {
                        validateResultItemDTO = appendItemError("Campus所属的Zone应为[" + matchZone + "]",
                                validateResultItemDTO);
                    }
                }
                if (dto.getSource().equals(DemandImportSourceEnum.PRODUCT_INPUT.getCh())) {
                    // 产品录入的需要校验
                    // 校验非空，以及是否在字典中， 项目类型
                    validateResultItemDTO = assertNotBlank("项目类别", dto.getProjSetName(),
                            validateResultItemDTO);
                    validateResultItemDTO = assertInDict("项目类别", dto.getProjSetName(), projSetNameSet,
                            validateResultItemDTO);
                    // 需求原因，非空，在字典内
                    validateResultItemDTO = assertNotBlank("需求原因", dto.getReason(), validateResultItemDTO);
                    validateResultItemDTO = assertInDict("需求原因", dto.getReason(), reasonSet,
                            validateResultItemDTO);
                    // obs项目类型，非空，在字典内
                    validateResultItemDTO = assertNotBlank("obs项目类型", dto.getObsProjectType(),
                            validateResultItemDTO);
                    List<Integer> demandYears = obsMap.get(dto.getObsProjectType());
                    if (ListUtils.isEmpty(demandYears)) {
                        validateResultItemDTO = appendItemError("规划产品所属部门不支持obs项目类型："
                                + dto.getObsProjectType() + "请联系wendyzjzhou进行配置", validateResultItemDTO);
                    } else {
                        List<Integer> validDemandYear = dto.getValidDemandYear();
                        for (Integer demandYear : validDemandYear) {
                            if (!demandYears.contains(demandYear)) {
                                validateResultItemDTO = appendItemError("规划产品所属部门不支持obs项目类型："
                                        + dto.getObsProjectType() + "请联系wendyzjzhou进行配置", validateResultItemDTO);
                            }
                        }
                    }
                    // 根据obs项目类型回填需求类型
                    // 常规项目 -》 常规
                    // 短租项目 -》 短租
                    // 其他项目类型 -》 项目
                    String demandType = DemandTypeEnum.PROJECT.getName();
                    if ("常规项目".equals(dto.getObsProjectType())) {
                        demandType = DemandTypeEnum.CONVENTIONAL.getName();
                    } else if ("短租项目".equals(dto.getObsProjectType())) {
                        demandType = DemandTypeEnum.SHORT_TERM_RENTAL.getName();
                    }
                    dto.setDemandType(demandType);
                    if (!StringUtils.isBlank(dto.getReason())) {
                        String reasonType = reasonMap.get(dto.getReason());
                        if (reasonType != null && reasonType.equals("客户需求")) {
                            boolean error = false;
                            if (!StringUtils.isBlank(dto.getIndustry())
                                    && dto.getIndustry().equals("常规")) {
                                error = true;
                            }
                            if (!StringUtils.isBlank(dto.getCustomerName())
                                    && dto.getCustomerName().equals("常规")) {
                                error = true;
                            }
                            if (error) {
                                validateResultItemDTO =
                                        appendItemError("您填写的需求原因为[" + dto.getReason()
                                                        + "]，必须指定一个明确的行业和客户",
                                                validateResultItemDTO);
                            }
                        }
                    }
                    // 业务类型，非空，在字典内
                    validateResultItemDTO = assertNotBlank("业务类型", dto.getModBusinessTypeName(),
                            validateResultItemDTO);
                    if (!campus2ModBizType.getOrDefault(dto.getCampus(), new ArrayList<>()).contains(dto.getModBusinessTypeName())) {
                        validateResultItemDTO = appendItemError("Campus[" + dto.getCampus()
                                        + "]不支持业务类型[" + dto.getModBusinessTypeName() + "]",
                                validateResultItemDTO);
                    }
                    // 设备类型，非空，在字典内
                    validateResultItemDTO = assertNotBlank("设备类型", dto.getDeviceType(),
                            validateResultItemDTO);
                    if (!deviceTypeSet.contains(dto.getDeviceType())) {
                        validateResultItemDTO = appendItemError("规划产品所属部门没有设备类型[" + dto.getDeviceType()
                                        + "]的资源权限，请联系kaijiazhang/dandiechen",
                                validateResultItemDTO);
                    }
                }
                if (validateResultItemDTO != null) {
                    validateResultDTO.appendItem(validateResultItemDTO);
                    validateResultItemDTO.setIndex(rowId);
                }
                rowId += 1;
            }
        }
        if (validateResultDTO.isHasError()) {
            // 提前返回校验结果，不更新DB
            return validateResultDTO;
        }
        // 需求分组检查，如果有分组则更新，无则插入
        if (flowInfoDO == null) {
            flowInfoDO = new FlowInfoDO();
            flowInfoDO.setExecTime(0);
            // 无用字段 维持非空即可
            flowInfoDO.setApprover("com_r");
            flowInfoDO.setCurrentStep(DemandFlowStepEnum.USER_NOT_SUBMIT.getId());
            flowInfoDO.setRrpConfigId(rrpConfigDO.getId());
            flowInfoDO.setProduct(product);
        }

        if (flowInfoDO.getCurrentStep() == null
                || flowInfoDO.getCurrentStep().equals(DemandFlowStepEnum.SYSTEM_INIT.getId())) {
            flowInfoDO.setCurrentStep(DemandFlowStepEnum.USER_NOT_SUBMIT.getId());
        }

        if (needSubmit) {
            // 需要提交 设置新提单人
            flowInfoDO.setFlowCreator(username);
        }

        // 插入或者更新flow_info
        rrpDBHelper.insertOrUpdate(flowInfoDO);
        // 判断是否存在，此处isExit()效率更高，原因是自动加了limit，找到一条匹配的就返回了，而不用查出所有的
        boolean exist = rrpDBHelper.isExist(ProductInfoDO.class,
                "where flowId = ?", flowInfoDO.getId());
        if (exist) {
            // 说明之前存在已经录入过的谁，备份一下 将数据迁移到product_info_logic_deleted表
            List<ProductInfoDO> originalDOs = rrpDBHelper.getAll(ProductInfoDO.class, "where flowId = ?",
                    flowInfoDO.getId());
            List<ProductInfoLogicDeletedDO> logicDeletedDOS = new ArrayList<>(originalDOs.size());
            // 获取一个uuid,用以表示同一个批次的预测信息
            String uuidStr = UUID.randomUUID().toString();
            for (ProductInfoDO originalDO : originalDOs) {
                logicDeletedDOS.add(ProductInfoLogicDeletedDO.tranToLogicDeleted(originalDO, flowInfoDO, uuidStr));
            }
            rrpDBHelper.insertBatchWithoutReturnId(logicDeletedDOS);
            rrpDBHelper.delete(ProductInfoDO.class,
                    "where flowId = ?", flowInfoDO.getId());
        }
        // 这里把 all 存起来，不用再查一次 DB，加快接口速度
        List<ProductInfoDO> all = new ArrayList<>();
        if (!CollectionUtils.isEmpty(data)) {
            // 需求类型的map
            Map<String, String> reasonMap = productDemandDictService.listAllReasonMap();
            for (ProductInfoSaveItemDTO dto : data) {
                for (YearMonthNumDTO numDTO : dto.getYearMonthValueList()) {
                    // 常规字段复制
                    ProductInfoDO productInfoDO = ProductInfoSaveItemDTO.toDO(dto, numDTO);
                    // 处理特殊字段
                    productInfoDO.setFlowId(flowInfoDO.getId());
                    productInfoDO.setProduct(product);
                    productInfoDO.setReasonType(reasonMap.get(productInfoDO.getReason()));
                    all.add(productInfoDO);
                }
            }
        }
        // 批量插入 不需要返回id
        rrpDBHelper.insertBatchWithoutReturnId(all);
        final FlowInfoDO asyncFlow = flowInfoDO;
        realTimeDemandService.syncReleaseFlowEvent(asyncFlow.getId(), username);
        realTimeDemandService.syncFlowStatus(asyncFlow.getId(), username, null);
        autoDeliveryDemandForecastService.autoCompleteSinglePlanProductOverExecutedForecast(product,
                rrpConfigDO.getId(), "保存", needSubmit);
        validateResultDTO.setFlowId(flowInfoDO.getId());
        return validateResultDTO;
    }

    // 带降级策略的获取机型
    private Set<String> getDeviceTypes(Map<String, Set<String>> deviceTypeMap, String key) {
        if (deviceTypeMap.get(key) != null) {
            return deviceTypeMap.get(key);
        }
        if (key.indexOf(';') == -1) {
            // 已经无法再降级了
            log.error("未能匹配到可用的机型,已经降级到：" + key);
            throw new BizException("未能匹配到可用的机型");
        }
        return getDeviceTypes(deviceTypeMap, key.substring(0, key.lastIndexOf(';')));
    }

    private ValidateResultItemDTO appendItemError(String error, ValidateResultItemDTO validateResultItemDTO) {
        if (validateResultItemDTO == null) {
            validateResultItemDTO = new ValidateResultItemDTO();
        }
        validateResultItemDTO.appendError(error);
        return validateResultItemDTO;
    }

    private ValidateResultItemDTO assertNotBlank(String filedName, Object value,
                                                 ValidateResultItemDTO validateResultItemDTO) {
        if (value instanceof String) {
            String str = (String) value;
            if (StringUtils.isBlank(str)) {
                return appendItemError(filedName + "不能为空", validateResultItemDTO);
            }
        } else if (value == null) {
            return appendItemError(filedName + "不能为空", validateResultItemDTO);
        }
        return validateResultItemDTO;
    }

    private ValidateResultItemDTO assertInDict(String filedName, Object value,
                                               Collection<?> dictSet,
                                               ValidateResultItemDTO validateResultItemDTO) {
        if (value == null) {
            return validateResultItemDTO;
        }
        if (value instanceof String) {
            String str = (String) value;
            if (!StringUtils.isBlank(str) && !dictSet.contains(str)) {
                return appendItemError(filedName + "不在字典范围内", validateResultItemDTO);
            }
        } else {
            if (!dictSet.contains(value)) {
                return appendItemError(filedName + "不在字典范围内", validateResultItemDTO);
            }
        }
        return validateResultItemDTO;
    }

    private Boolean hasThisPlanProductOrCOMDAuth(String username, String planProduct) {
        return hasThisPlanProductOrCOMDAuth(username, planProduct, false);
    }

    private Boolean hasThisPlanProductOrCOMDAuth(String username, String planProduct, boolean withOnlySee) {
        Set<AuthRoleEnum> roles = new HashSet<>();
        roles.add(AuthRoleEnum.PRODUCT_SUBMITER);
        roles.add(AuthRoleEnum.PRODUCT_APPROVER);
        if (withOnlySee) {
            roles.add(AuthRoleEnum.PRODUCT_VIEWER);
        }
        Set<String> planProductSet = myAuthService.listMyPlanProduct(username,
                roles);
        return (!CollectionUtils.isEmpty(planProductSet) && planProductSet.contains(planProduct))
                || myAuthService.hasCOMDRole(username);
    }

}
