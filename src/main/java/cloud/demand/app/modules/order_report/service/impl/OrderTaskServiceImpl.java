package cloud.demand.app.modules.order_report.service.impl;

import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.SimpleDepTaskEnum;
import cloud.demand.app.modules.mrpv2.task.process.BatchCommonTaskProcess;
import cloud.demand.app.modules.mrpv2.utils.PageQueryUtils;
import cloud.demand.app.modules.order.entity.OrderItemDO;
import cloud.demand.app.modules.order.entity.PplOrderTagLogDO;
import cloud.demand.app.modules.order_report.dto.resp.other.OrderScoreDiff;
import cloud.demand.app.modules.order_report.entity.other.OrderInfoItemVO;
import cloud.demand.app.modules.order_report.entity.report.DwsOrderScoreItemDfDO;
import cloud.demand.app.modules.order_report.entity.report.OrderScoreItem;
import cloud.demand.app.modules.order_report.service.OrderScoreItemService;
import cloud.demand.app.modules.order_report.service.OrderTaskService;
import cloud.demand.app.modules.order_report.utils.TransactionUtils;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.enums.TaskStatus;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import erp.base.fiber.support.dispatcher.FiberSupplier;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import yunti.boot.exception.BizException;

/** 订单评分任务 */
@Slf4j(topic = "订单评分任务")
@Service
public class OrderTaskServiceImpl implements OrderTaskService {

    @Resource
    private BatchCommonTaskProcess process;

    @Resource
    private OrderScoreItemService orderScoreItemService;
    
    @Resource
    private DBHelper demandDBHelper;
    
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    /** 初始化任务 */
    @Transactional(transactionManager = "demandTransactionManager",rollbackFor = Exception.class)
    @Override
    public boolean initTask() {
        LocalDate now = LocalDate.now();
        String version = DateUtils.formatDate(now);
        // 校验是否有运行中或者未完成的任务
        if (!checkTask(version)){
            log.error("有运行中或者未完成的任务，请勿重复执行");
            return false;
        }

        String batchId = SoeCommonUtils.getBatchId();
        process.initTask(new SimpleCommonTask(version,batchId), SimpleDepTaskEnum.ORDER_SCORE_DWD_BASE);
        process.initTask(new SimpleCommonTask(version,batchId), SimpleDepTaskEnum.ORDER_SCORE_DWS_SCORE);
        return true;
    }

    @Override
    public void syncInitTask(Set<String> orderNumber) {
        PageQueryUtils.afterCommit(new FiberSupplier() {
            @Override
            public void consume() {
                if (ListUtils.isNotEmpty(orderNumber)){
                    // 等待上层事务完成
                    orderScoreItemService.updateData(new ArrayList<>(orderNumber));
                    initTask();
                }
            }
        });
    }

    @Override
    public List<OrderScoreDiff> checkDiff() {
        // step1：获取 mysql 和 ck 数据（mysql 是实时的，ck 有 1 分钟延迟）
        List<OrderScoreItem> mysqlData = demandDBHelper.getAll(OrderScoreItem.class);
        List<DwsOrderScoreItemDfDO> ckData = ckcldStdCrpDBHelper.getAll(DwsOrderScoreItemDfDO.class, "where stat_time = ?",
                LocalDate.now());
        Map<String, OrderScoreItem> mysqlDataMap = ListUtils.toMap(mysqlData, OrderScoreItem::getId,
                item -> item);
        Map<String, DwsOrderScoreItemDfDO> ckDataMap = ListUtils.toMap(ckData, DwsOrderScoreItemDfDO::getId,
                item -> item);

        // step2：检查是否有重复的 id（id 是 orderNumber + hex（维度））
        if (mysqlDataMap.size() != mysqlData.size()){
            // 有重复的 id
            List<String> errorId = new ArrayList<>();
            List<Long> errorAutoId = new ArrayList<>();
            for (OrderScoreItem mysqlDatum : mysqlData) {
                OrderScoreItem orderScoreItem = mysqlDataMap.get(mysqlDatum.getId());
                // 如果map 的 value 不是同一个对象，说明有重复的 id
                if (!Objects.equals(orderScoreItem.getAutoId(), mysqlDatum.getAutoId())){
                    errorAutoId.add(mysqlDatum.getAutoId());
                    errorId.add(mysqlDatum.getId());
                }
            }
            throw new IllegalStateException(String.format("有重复的 id：[%s]，auto_id :[%s]", errorId,errorAutoId));
        }
        List<OrderScoreDiff> ret = new ArrayList<>();
        mysqlDataMap.forEach((k,v)->{
            DwsOrderScoreItemDfDO remove = ckDataMap.remove(k);
            if (remove == null){
                ret.add(OrderScoreDiff.buildCkDataNotExist(v.getId(),v.getAutoId()));
            }else {
                BigDecimal totalScore = remove.getTotalScore();
                BigDecimal adjTotalScore = remove.getAdjTotalScore();

                // 评分不一致
                if (totalScore.compareTo(v.getTotalScore()) != 0 || adjTotalScore.compareTo(v.getAdjTotalScore()) != 0){
                    ret.add(OrderScoreDiff.buildDataDiff(v,remove));
                }
            }
        });

        if (ListUtils.isNotEmpty(ckDataMap)){
            ckDataMap.forEach((k,v)-> ret.add(OrderScoreDiff.buildMysqlDataNotExist(v.getId())));
        }

        return ret;
    }

    @Override
    public Map<String, List<String>> checkOrderTag() {
        List<PplOrderTagLogDO> tagLogList;
        List<OrderInfoItemVO> orderInfoList;
        // 获取所有订单打标结果
        tagLogList = demandDBHelper.getAll(PplOrderTagLogDO.class);
        // 获取所有订单明细信息(只看available状态的，不然 item 的子单不唯一，剔除草稿和取消的)
        orderInfoList = demandDBHelper.getAll(OrderInfoItemVO.class, "where available_status = 'available' and order_category in ('CVM', 'GPU', 'BARE_METAL') and order_status not in ('DRAFT','CANCELED')");

        // 订单信息转 map：key：orderNumberId
        Set<String> orderNumberIdSet = new HashSet<>();

        for (OrderInfoItemVO info : orderInfoList) {
            if (ListUtils.isNotEmpty(info.getItems())) {
                for (OrderItemDO item : info.getItems()) {
                    String orderNumberId = item.getOrderNumberId();
                    if (orderNumberIdSet.contains(orderNumberId)) {
                        throw new BizException(String.format("订单明细信息重复，orderNumberId：【%s】", orderNumberId));
                    }
                    orderNumberIdSet.add(orderNumberId);
                }
            }
        }

        Map<String, List<String>> ret = new HashMap<>();

        // 校验打标信息和订单信息
        Set<String> itemNotHasId = new HashSet<>(); // 明细没有的 id
        Set<String> tagNotHasId = new HashSet<>(orderNumberIdSet); // 打标没有的 id
        Set<String> tagDoubleId = new HashSet<>(); // 打标重复的 id
        for (PplOrderTagLogDO tagLogDO : tagLogList) {
            String orderNumberId = tagLogDO.getOrderNumberId();
            if (!orderNumberIdSet.contains(orderNumberId)){ // 明细不包含算到明细没有 id 里面
                itemNotHasId.add(orderNumberId);
            }else if (!tagNotHasId.remove(orderNumberId)){ // 打标包含算到打标没有 id 里面，如果remove 失败说明说明重复remove，记到 double id 里面
                tagDoubleId.add(orderNumberId);
            }
        }
        if (ListUtils.isNotEmpty(itemNotHasId)){
            ret.put("订单明细缺失", new ArrayList<>(itemNotHasId));
        }

        if (ListUtils.isNotEmpty(tagNotHasId)){
            ret.put("订单打标缺失", new ArrayList<>(tagNotHasId));
        }

        if (ListUtils.isNotEmpty(tagDoubleId)){
            ret.put("订单打标重复", new ArrayList<>(tagDoubleId));
        }

        return ret;
    }

    /**
     * 校验是否已经有任务了，（status in (NEW,ERROR,MANUAL_STOP) ），注意：RUNNING状态的任务不算，因为已经执行中了
     * @param version 版本
     * @return 返回是否有记录
     */
    private boolean checkTask(String version) {
        String sql = "where name = ? and version = ? and status in (?) limit 1 for update";
        DBHelper dbHelper = process.getDbHelper();
        long count = dbHelper.getCount(SimpleCommonTask.class, sql,
                SimpleDepTaskEnum.ORDER_SCORE_DWD_BASE.getName(),
                version,
                ListUtils.newList(TaskStatus.NEW.getName(), TaskStatus.ERROR.getName()));
        return count == 0;
    }
}
