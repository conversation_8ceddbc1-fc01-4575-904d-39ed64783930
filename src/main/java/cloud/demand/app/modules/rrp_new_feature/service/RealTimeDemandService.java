package cloud.demand.app.modules.rrp_new_feature.service;

import cloud.demand.app.entity.rrp.RRPDemandItemWithVersionDO;
import cloud.demand.app.modules.rrp_new_feature.entity.Product13WeekDemandRealTimeItemDO;
import cloud.demand.app.modules.rrp_new_feature.service.impl.RealTimeDemandServiceImpl.ClearRealTimeItemDTO;
import cloud.demand.app.modules.rrp_new_feature.service.impl.RealTimeDemandServiceImpl.MissRealTimeItem;
import cloud.demand.app.modules.rrp_new_feature.service.impl.RealTimeDemandServiceImpl.OrderIdExecutedRelation;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface RealTimeDemandService {

    Map<String, Object> verify();

    void autoFix(Map<String, Object> verifyResult);

    /**
     * 根据13周预测版本化的填写详情重建实时预测表，重建后，id一定会发生变化，且PPL id会丢失，请谨慎使用该接口
     */
    void rebuild();


    /**
     * 外部修改了某个版本预测的id，通知实时预测接口更新相应的数据，此时，实时预测这边会实时查一遍当前的版本预测情况（不用考虑先来后到的问题，因为都是最新的）
     * 再对实时预测进行更新
     * 关键点：这个接口要加分布式锁，因为可能不同id会同时更新到同样的数据，让它退化成先来后到的问题进行解决
     *
     * @param versionItemIds 版本预测的id列表
     * @param username 用户名
     * @param context 一个用于上下文传递的参数map
     */
    void syncChangeToRealTime(Collection<Long> versionItemIds, String username,
            Map<String, Object> context);

    /**
     * 同步流程的释放事件到实时预测
     *
     * @param releaseFlowId 被释放的流程id
     * @param username 用户名
     */
    void syncReleaseFlowEvent(Long releaseFlowId, String username);

    /**
     * 批量同步流程的释放事件到实时预测
     *
     * @param releaseFlowIds 被释放的流程id列表
     * @param username 用户名
     */
    void syncReleaseFlowEvent(Collection<Long> releaseFlowIds, String username);

    /**
     * 同步流程的状态到实时预测(底层逻辑，流程对应一批版本预测，把这些版本预测id传入到syncChangeToRealTime)
     *
     * @param flowId 流程的id
     * @param username 用户名
     * @param context 一个用于上下文传递的参数map
     */
    void syncFlowStatus(Long flowId, String username, Map<String, Object> context);

    /**
     * 同步流程的状态到实时预测(底层逻辑，流程对应一批版本预测，把这些版本预测id传入到syncChangeToRealTime)
     *
     * @param flowIds 流程的id列表
     * @param username 用户名
     * @param context 一个用于上下文传递的参数map
     */
    void syncFlowStatus(Collection<Long> flowIds, String username, Map<String, Object> context);

    /**
     * 在事务内，比对更新实时表
     *
     * @param rrpMap 版本预测map
     * @param realTimeMap 实时预测map
     * @param username 用户名
     * @param productMaxVersionIdMap 每个产品对应的最大版本idmap
     */
    void baseSyncChangeToRealTime(Map<String, RRPDemandItemWithVersionDO> rrpMap,
            Map<String, Product13WeekDemandRealTimeItemDO> realTimeMap, String username,
            Map<String, Long> productMaxVersionIdMap, Map<String, Object> context);

    /**
     * 清理实时预测表中对应的item（数量变0），无需事务
     *
     * @param versionId 版本id
     * @param planProduct 规划产品
     */
    void baseClearRealTimeItem(Long versionId, String planProduct);

    /**
     * 清理实时预测表中对应的item（数量变0），无需事务
     *
     * @param dtos 含版本 id 和规划产品的列表
     */
    void baseClearRealTimeItem(List<ClearRealTimeItemDTO> dtos);

    /**
     * 清理实时预测表中对应的item（数量变0），无需事务
     *
     * @param flowId 流程id
     * @param username 用户名
     */
    void clearRealTimeItem(Long flowId, String username);

    /**
     * 清除上下游的传递记录
     *
     * @param industryVersion 13周行业版本号
     * @param rrpConfigId 13周产品ID
     * @param industryName 行业名
     */
    void clearUpToDownPassRecord(String industryVersion, Integer rrpConfigId, String industryName);

    /**
     * 清除上下游的传递记录
     *
     * @param industryVersion 13周行业版本号
     * @param rrpConfigId 13周产品ID
     */
    void clearUpToDownPassRecord(String industryVersion, Integer rrpConfigId);

    void transactionSyncFromOrderId(Set<String> orderIdSet,
            Map<String, OrderIdExecutedRelation> orderIdExecutedRelationMap);

    void autoAppend(Map<String, MissRealTimeItem> missRealTimeItemMap, Map<String, Object> context);

    void refreshVersionTable();

    void syncOrderExecuted(Collection<String> orderIds, Map<String, Object> context);

    void scheduleRefreshCurVersionOrderExecuted();

    void scheduleRefreshCurVersionOrderExecuted(boolean addThisMonth);
}
