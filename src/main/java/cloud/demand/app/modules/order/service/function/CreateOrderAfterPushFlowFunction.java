package cloud.demand.app.modules.order.service.function;

import cloud.demand.app.modules.order.entity.OrderInfoDO;

/**
 *  生成订单信息的接口<br/>
 *  相关接口的执行顺序<br/>
 *  <ol>
 *      <li>执行 {@link CreateOrderAfterPushFlowFunction}</li>
 *      <li>执行 {@link OrderCurrentProcessorCreator} 获取处理人，然后自动为订单信息的处理人赋值</li>
 *      <li>上面生成的新订单 写入数据库</li>
 *      <li>执行 {@link CreateOrderItemAfterPushFlowFunction}</li>
 *  </ol>
 */
public interface CreateOrderAfterPushFlowFunction {

    /**
     * 自定义的订单记录生成方法，一般用于修改订单业务信息的操作中，为null时默认是从原订单复制生成 <br/>
     * 必须返回 新的 订单对象 <br/>
     * 必须返回 新的 订单对象 <br/>
     * 必须返回 新的 订单对象 <br/>
     * 此方法内不要对生成的订单进行 insert 操作 <br/>
     * 此方法内不要对生成的订单进行 insert 操作 <br/>
     * 此方法内不要对生成的订单进行 insert 操作 <br/>
     * 具体的 insert 操作会在统一封装的 {@link cloud.demand.app.modules.order.service.OrderFlowService} 中进行 <br/><br/>
     *
     * 此方法在 {@link OrderCurrentProcessorCreator} 之前进行 <br/>
     * 此方法在 {@link CreateOrderItemAfterPushFlowFunction} 之前进行 <br/>
     *
     * @param normalOrder 原订单信息，订单主流程刚启动时这个值为 null
     */
    OrderInfoDO create(OrderInfoDO normalOrder);

}
