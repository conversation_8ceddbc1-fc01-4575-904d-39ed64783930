package cloud.demand.app.modules.order.entity;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;

/**
 *  订单中大盘满足部分的满足度计算的切片信息
 */
@Data
@Table("order_satisfy_rate")
public class OrderSatisfyRateDO extends BaseDO {

    @Column("begin_date")
    private LocalDate beginDate;

    @Column("end_date")
    private LocalDate endDate;

    @Column("instance_type")
    private String instanceType;

    @Column("zone_name")
    private String zoneName;

    /**
     *  线上库存，实际库存（A）
     */
    @Column("online_stock_core")
    private BigDecimal onlineStockCore;

    /**
     *  在途库存（B）
     */
    @Column("on_way_stock_core")
    private BigDecimal onWayStockCore;

    /**
     *  总库存（C=A+B）
     */
    @Column("total_stock_core")
    private BigDecimal totalStockCore;

    /**
     *  订单需求核心数，大盘满足的需求核心数（D）
     */
    @Column("demand_total_core")
    private BigDecimal demandTotalCore;

    /**
     *  已履约核心数（E）
     */
    @Column("keep_promise_core")
    private BigDecimal keepPromiseCore;

    /**
     *  已预扣核心数（F）
     */
    @Column("pre_deduct_core")
    private BigDecimal preDeductCore;

    /**
     * 参与对冲的需求核心数（G = D-（Max（E，F）））
     */
    @Column("wait_match_core")
    private BigDecimal waitMatchCore;

    /**
     *  对冲满足核心数（H = Min（G，C））
     */
    @Column("matched_core")
    private BigDecimal matchedCore;

    /**
     *  对冲未满足核心数（I = G-H）
     */
    @Column("not_match_core")
    private BigDecimal notMatchCore;

    /**
     *  对冲剩余库存核心数（J = Max(C-G，0)）
     */
    @Column("remain_stock_core")
    private BigDecimal remainStockCore;

    /**
     *  订单已满足核心数，大盘满足需求中（K = H +（Max（E，F））
     */
    @Column("order_satisfy_core")
    private BigDecimal orderSatisfyCore;

}
