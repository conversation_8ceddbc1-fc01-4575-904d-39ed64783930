package cloud.demand.app.modules.order.entity;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.modules.order.dto.ElasticCycleConfig;
import cloud.demand.app.modules.order.dto.ElasticCycleConfig.PreDeductPlanTime;
import cloud.demand.app.modules.order.dto.PreDeductGridSimpleDTO;
import cloud.demand.app.modules.order.dto.resp.OrderSupplyPlanDetailWithPlanDTO;
import cloud.demand.app.modules.order.enums.OrderElasticType;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple2;
import io.vavr.Tuple3;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.ToString;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;
import yunti.boot.exception.BizException;

/**
 * 周期性订单月度满足量<br/>
 */
@Data
@ToString
@Table("order_cycle_elastic_month_satisfy")
public class OrderCycleElasticMonthSatisfyDO extends BaseDO {

    @Column(value = "available")
    private Boolean available;

    @Column(value = "stat_date")
    private LocalDate statDate;

    /** 业务订单号，一个订单的唯一标识<br/> */
    @Column(value = "order_number")
    private String orderNumber;

    /** 实例类型<br/> */
    @Column(value = "instance_type")
    private String instanceType;

    /** 可用区<br/> */
    @Column(value = "zone_name")
    private String zoneName;

    /** 开始购买时间<br/> */
    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    /** 结束购买时间<br/> */
    @Column(value = "end_buy_date")
    private LocalDate endBuyDate;

    /** 需求量-cpu核数<br/> */
    @Column(value = "demand_cpu_num")
    private Integer demandCpuNum;

    /** 需求量-gpu卡数<br/> */
    @Column(value = "demand_gpu_num")
    private Integer demandGpuNum;

    /** 已满足量-cpu核数<br/> */
    @Column(value = "satisfied_cpu_num")
    private BigDecimal satisfiedCpuNum;

    /** 已满足量-gpu卡数<br/> */
    @Column(value = "satisfied_gpu_num")
    private BigDecimal satisfiedGpuNum;

    /** 产品<br/> */
    @Column(value = "product")
    private String product;

    @Column(value = "data_year_month")
    private String dataYearMonth;


    public static List<OrderCycleElasticMonthSatisfyDO> fromPreDeductGridList(List<OrderSupplyPlanDetailWithPlanDTO> list,
            OrderInfoDO order, LocalDate statDate, List<PreDeductGridSimpleDTO> deductList,
            ElasticCycleConfig config)  {
        if (ListUtils.isEmpty(list) || order == null) {
            return new ArrayList<>();
        }
        if (deductList == null) {
            deductList = new ArrayList<>();
        }
        OrderElasticType elasticType = OrderElasticType.getByTypeName(order.getElasticType());
        if (elasticType == null || !order.cycleElasticReturnTrue()) {
            throw BizException.makeThrow("非日弹性、周弹性、月弹性订单：%s", order.getOrderNumber());
        }
        String orderNumber = order.getOrderNumber();
        List<OrderSupplyPlanDetailWithPlanDTO> datas = list.stream().filter(o ->
                        o != null && o.getPlan() != null
                                && Strings.isNotBlank(o.getOrderNumber()) && Objects.equals(o.getOrderNumber(), orderNumber))
                .collect(Collectors.toList());
        ListUtils.sortAscNullLast(datas, OrderSupplyPlanDetailDO::getConsensusBeginBuyDate);
        if (ListUtils.isEmpty(datas)) {
            return new ArrayList<>();
        }
        Map<String, List<PreDeductGridSimpleDTO>> deuctMap = new HashMap<>();
        for (PreDeductGridSimpleDTO itemDO : deductList) {
            if (itemDO == null || !Objects.equals(orderNumber, itemDO.getOrderNumber())) {
                continue;
            }
            itemDO.initSomeData();
            String key = String.join("@", itemDO.getPlanZoneName(), itemDO.getPlanInstanceType());
            List<PreDeductGridSimpleDTO> gridList = deuctMap.computeIfAbsent(key, k -> new ArrayList<>());
            gridList.add(itemDO);
        }

        Map<String, OrderCycleElasticMonthSatisfyDO> tmpMap = new HashMap<>();
        List<OrderCycleElasticMonthSatisfyDO> tmp = new ArrayList<>();
        for (OrderSupplyPlanDetailWithPlanDTO detail : datas) {
            String supplyKey =  String.join("@", detail.getSupplyZoneName(), detail.getSupplyInstanceType(),
                    detail.getConsensusBeginBuyDate().toString());

            OrderCycleElasticMonthSatisfyDO item = tmpMap.get(supplyKey);
            if (item == null) {
                item = new OrderCycleElasticMonthSatisfyDO();
                tmpMap.put(supplyKey, item);
                tmp.add(item);

                item.setInstanceType(detail.getSupplyInstanceType());
                item.setBeginBuyDate(detail.getConsensusBeginBuyDate());
                item.setEndBuyDate(detail.getConsensusEndBuyDate());
                item.setOrderNumber(detail.getOrderNumber());
                item.setProduct(order.getProduct());
                item.setZoneName(detail.getSupplyZoneName());
                item.setStatDate(statDate);
                item.setAvailable(true);
            }
            if (item.getEndBuyDate().isBefore(detail.getConsensusEndBuyDate())) {
                item.setEndBuyDate(detail.getConsensusEndBuyDate());
            }

            int supplyCore = detail.getSupplyCoreNum() == null ? 0 : detail.getSupplyCoreNum();
            int normalCore = item.getDemandCpuNum() == null ? 0 : item.getDemandCpuNum();
            item.setDemandCpuNum(normalCore + supplyCore);

            int supplyGpu = detail.getSupplyGpuNum() == null ? 0 : detail.getSupplyGpuNum();
            int normalGpu = item.getDemandGpuNum() == null ? 0 : item.getDemandGpuNum();
            item.setDemandGpuNum(normalGpu + supplyGpu);

        }

        List<OrderCycleElasticMonthSatisfyDO> result = new ArrayList<>();
        for (OrderCycleElasticMonthSatisfyDO item : tmp) {
            String deductKey = String.join("@", item.getZoneName(), item.getInstanceType());
            List<PreDeductGridSimpleDTO> gridList = deuctMap.getOrDefault(deductKey, new ArrayList<>());
            Map<LocalDate, List<PreDeductGridSimpleDTO>> deductDateMap = ListUtils.toMapList(gridList,
                    PreDeductGridSimpleDTO::getDate, Function.identity());

            if (elasticType == OrderElasticType.BY_DAY) {
                LocalDate end =  item.getEndBuyDate().isAfter(statDate)
                        ?  statDate : item.getEndBuyDate();
                // 按月切分
                List<Tuple2<LocalDate, LocalDate>> monthList = splitByMonth(item.getBeginBuyDate(), end);
                if (monthList.isEmpty()) {
                    continue;
                }
                for (Tuple2<LocalDate, LocalDate> tuple2 : monthList) {
                    // 日弹性
                    OrderCycleElasticMonthSatisfyDO oneMonth = oneMonth(tuple2, item, deductDateMap);
                    result.add(oneMonth);
                }
            } else {
                // 按月切分
                List<List<PreDeductPlanTime>> monthList = splitByMonth(config.getPreDeductPlanTimeList(), statDate);
                if (monthList.isEmpty()) {
                    continue;
                }
                for (List<PreDeductPlanTime> planTimeList : monthList) {
                    if (ListUtils.isEmpty(planTimeList)) {
                        continue;
                    }
                    // 周弹性、月弹性
                    OrderCycleElasticMonthSatisfyDO oneMonth = oneMonth(planTimeList, item, deductDateMap);
                    if (oneMonth != null) {
                        result.add(oneMonth);
                    }
                }
            }
        }
        return result;
    }

    private static OrderCycleElasticMonthSatisfyDO oneMonth(List<PreDeductPlanTime> planTimeList,
            OrderCycleElasticMonthSatisfyDO source,
            Map<LocalDate, List<PreDeductGridSimpleDTO>> deductDateMap) {
        // 周弹性、月弹性：共识维度满足量 = Sum(有效周期的满足量) / 有效周期数量 。
        // 其中 单个周期满足量 = 周期内，预扣单累计预扣成功数量
        int cycleCount = 0;
        BigDecimal cycleTotalDeductCore = BigDecimal.ZERO;
        BigDecimal cycleTotalDeductGpu = BigDecimal.ZERO;
        // 每天需要的核心数、卡数
        BigDecimal dayWaitAllocateCore = new BigDecimal(source.getDemandCpuNum());
        BigDecimal dayWaitAllocateGpu = new BigDecimal(source.getDemandGpuNum());

        OrderCycleElasticMonthSatisfyDO oneMonth = new OrderCycleElasticMonthSatisfyDO();
        BeanUtils.copyProperties(source, oneMonth);
        LocalDate beginBuyDate = null;
        LocalDate endBuyDate = null;
        for (PreDeductPlanTime planTime : planTimeList) {
            if (source.getStatDate().isBefore(planTime.getStartPreDeductDate())) {
                // 未到周期预扣开始日期，不纳入周期计算
                continue;
            }
            if (beginBuyDate == null || beginBuyDate.isAfter(planTime.getStartPreDeductDate())) {
                beginBuyDate = planTime.getStartPreDeductDate();
            }
            if (endBuyDate == null || endBuyDate.isBefore(planTime.getEndPreDeductDate())) {
                endBuyDate = planTime.getEndPreDeductDate();
            }
            LocalDate end =  planTime.getEndPreDeductDate().isAfter(source.getStatDate())
                    ?  source.getStatDate() : planTime.getEndPreDeductDate();
            Tuple3<BigDecimal, BigDecimal, Boolean> oneCycleRes = OrderItemSatisfyRateDO.oneCycleMax(
                    planTime.getStartPreDeductDate(),
                    end, dayWaitAllocateCore, dayWaitAllocateGpu, deductDateMap);
            cycleTotalDeductCore = cycleTotalDeductCore.add(oneCycleRes._1);
            cycleTotalDeductGpu = cycleTotalDeductGpu.add(oneCycleRes._2);
            if (oneCycleRes._3 != null && oneCycleRes._3) {
                // 有预扣才算有效周期
                cycleCount++;
            }
        }
        if (beginBuyDate == null) {
            return null;
        }
        oneMonth.setBeginBuyDate(beginBuyDate);
        oneMonth.setEndBuyDate(endBuyDate);
        oneMonth.setDataYearMonth(YearMonth.from(oneMonth.beginBuyDate).toString());

        BigDecimal totalMatchCore =  BigDecimal.ZERO;
        BigDecimal totalMatchGpu =  BigDecimal.ZERO;
        if (cycleCount > 0) {
            totalMatchCore = cycleTotalDeductCore.divide(new BigDecimal(cycleCount),
                    2, RoundingMode.HALF_UP);
            totalMatchGpu = cycleTotalDeductGpu.divide(new BigDecimal(cycleCount),
                    2, RoundingMode.HALF_UP);
        }

        oneMonth.setSatisfiedCpuNum(totalMatchCore);
        oneMonth.setSatisfiedGpuNum(totalMatchGpu);
        return oneMonth;
    }

    private static OrderCycleElasticMonthSatisfyDO oneMonth(Tuple2<LocalDate, LocalDate> tuple2,
            OrderCycleElasticMonthSatisfyDO source,
            Map<LocalDate, List<PreDeductGridSimpleDTO>> deductDateMap) {
        LocalDate beginBuyDate = tuple2._1;
        LocalDate endBuyDate = tuple2._2;
        OrderCycleElasticMonthSatisfyDO oneMonth = new OrderCycleElasticMonthSatisfyDO();
        BeanUtils.copyProperties(source, oneMonth);
        oneMonth.setBeginBuyDate(beginBuyDate);
        oneMonth.setEndBuyDate(endBuyDate);
        // 每天需要的核心数、卡数
        BigDecimal dayWaitAllocateCore = new BigDecimal(source.getDemandCpuNum());
        BigDecimal dayWaitAllocateGpu = new BigDecimal(source.getDemandGpuNum());
        // 日弹性：共识维度满足量 = Sum(预扣成功量)/共识需求天数
        Tuple2<BigDecimal, BigDecimal> oneCycleRes = OrderItemSatisfyRateDO.oneCycleAvg(
                beginBuyDate, endBuyDate, dayWaitAllocateCore, dayWaitAllocateGpu, deductDateMap);
        oneMonth.setSatisfiedCpuNum(oneCycleRes._1);
        oneMonth.setSatisfiedGpuNum(oneCycleRes._2);
        oneMonth.setDataYearMonth(YearMonth.from(oneMonth.beginBuyDate).toString());
        return oneMonth;
    }

    private static List<Tuple2<LocalDate, LocalDate>> splitByMonth(LocalDate begin, LocalDate end) {
        List<Tuple2<LocalDate, LocalDate>> result = new ArrayList<>();
        LocalDate current = begin;
        int count = 0;
        while (!current.isAfter(end)) {
            count++;
            if (count > 1000) {
                throw BizException.makeThrow("循环异常，联系dotyou排查问题");
            }
            LocalDate monthStart = current.withDayOfMonth(1);
            LocalDate monthEnd = current.withDayOfMonth(current.lengthOfMonth());
            // 如果是第一个月，起始日期为begin
            if (current.equals(begin)) {
                monthStart = begin;
            }
            // 如果是最后一个月，结束日期为end
            if (monthEnd.isAfter(end)) {
                monthEnd = end;
            }
            result.add(new Tuple2<>(monthStart, monthEnd));
            // 移动到下个月的第一天
            current = monthEnd.plusDays(1);
        }
        return result;
    }

    private static List<List<PreDeductPlanTime>> splitByMonth(List<PreDeductPlanTime> config, LocalDate statDate) {
        Map<YearMonth, List<PreDeductPlanTime>> result = new HashMap<>();
        for (PreDeductPlanTime item : config) {
            if (statDate.isBefore(item.getStartPreDeductDate())) {
                // 未到周期预扣开始日期，不纳入周期计算
                continue;
            }
            YearMonth yearMonth = YearMonth.from(item.getStartPreDeductDate());
            List<PreDeductPlanTime> list = result.computeIfAbsent(yearMonth, k -> new ArrayList<>());
            list.add(item);
        }
        return new ArrayList<>(result.values());
    }

}
