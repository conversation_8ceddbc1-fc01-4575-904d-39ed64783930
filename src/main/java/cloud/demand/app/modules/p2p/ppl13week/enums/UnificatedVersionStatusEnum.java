package cloud.demand.app.modules.p2p.ppl13week.enums;

import java.util.Objects;
import lombok.Getter;

@Getter
public enum UnificatedVersionStatusEnum {

    NEW("NEW", "待启动"),

    PROCESS("PROCESS", "进行中"),

    DONE("DONE", "已关闭");

    final private String code;
    final private String name;

    UnificatedVersionStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static UnificatedVersionStatusEnum getByCode(String code) {
        for (UnificatedVersionStatusEnum e : UnificatedVersionStatusEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        UnificatedVersionStatusEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}