package cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp;

import cloud.demand.app.modules.p2p.ppl13week.service.filler.CommonCustomerShortNameFiller;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class QueryInfoByUinRsp implements CommonCustomerShortNameFiller {

    Boolean isExist;

    String warZone; // 战区
    String industry; // 行业
    String customerShortName; //简称
    String customerName; // 名称
    String customerSource;// 客户来源

    String industryDept; // 行业部门

    String systemArchitect; //所属架构师

    Integer appId; // appId

    String commonCustomerShortName; // 通用客户简称

    public QueryInfoByUinRsp(Boolean isExist) {
        this.isExist = isExist;
    }

    public QueryInfoByUinRsp(Boolean isExist, String warZone, String industry, String customerShortName,
            String customerName, String customerSource, String industryDept,
            String systemArchitect, Integer appId) {
        this.isExist = isExist;
        this.warZone = warZone;
        this.industry = industry;
        this.customerShortName = customerShortName;
        this.customerName = customerName;
        this.customerSource = customerSource;
        this.industryDept = industryDept;
        this.systemArchitect = systemArchitect;
        this.appId = appId;
    }


    @Override
    public String provideCustomerShortName() {
        return this.customerShortName;
    }

    @Override
    public String provideIndustryDept() {
        return this.industryDept;
    }

    @Override
    public void fillCommonCustomerShortName(String commonCustomerShortName) {
        this.commonCustomerShortName = commonCustomerShortName;
    }
}
