package cloud.demand.app.modules.p2p.longterm.service.impl;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermGroupDTO;
import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermGroupItemDTO;
import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermGroupReasonDTO;
import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermGroupRejectInfoDTO;
import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermTimeDTO;
import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermVersionHistoryItemDTO;
import cloud.demand.app.modules.p2p.longterm.controller.req.GenTimesReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.QueryLongtermGroupItemReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.ReplaceIntervened13weekReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.SaveLongtermGroupItemReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.SaveLongtermGroupReasonReq;
import cloud.demand.app.modules.p2p.longterm.controller.resp.QueryLongtermGroupItemResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.ReplaceIntervened13weekResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.SaveLongtermGroupItemResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.SaveLongtermGroupReasonResp;
import cloud.demand.app.modules.p2p.longterm.dto.LongtermPpl13weekDataDTO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupReasonDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupRecordDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionHistoryItemDO;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupItemSourceTypeEnum;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupItemTimeUnitEnum;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupStatusEnum;
import cloud.demand.app.modules.p2p.longterm.key_accessor.v1_key_getter.LongtermItemBizTimeDimGK;
import cloud.demand.app.modules.p2p.longterm.key_accessor.v1_key_getter.LongtermReasonDimGK;
import cloud.demand.app.modules.p2p.longterm.service.LongTermVersionService;
import cloud.demand.app.modules.p2p.longterm.service.LongtermDictService;
import cloud.demand.app.modules.p2p.longterm.service.LongtermGroupItemService;
import cloud.demand.app.modules.p2p.longterm.service.LongtermVersionApprovalService;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplConfigProductEnumDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@SuppressWarnings("SpringTransactionalComponentInspection")
@Service
@Slf4j
public class LongtermGroupItemServiceImpl implements LongtermGroupItemService {

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private LongTermVersionService longTermVersionService;
    @Resource
    private LongtermDictService longtermDictService;
    @Resource
    private LongtermVersionApprovalService approvalService;
    @Resource
    private DBHelper cdCommonDbHelper;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Override
    public QueryLongtermGroupItemResp queryLongtermGroupItem(QueryLongtermGroupItemReq req) {
        // 1. 查询版本相关信息
        LongtermVersionGroupDO groupDO = demandDBHelper.getOne(LongtermVersionGroupDO.class, "where id=?",
                req.getGroupId());
        if (groupDO == null) {
            throw new WrongWebParameterException(
                    "分组Id:" + req.getGroupId() + "不存在，可能已经被删除，请回退到中长期版本列表");
        }
        LongtermVersionDO versionDO = demandDBHelper.getOne(LongtermVersionDO.class, "where version_code=?",
                groupDO.getVersionCode());
        if (versionDO == null) {
            throw new WrongWebParameterException(
                    "版本号:" + groupDO.getVersionCode() + "不存在，可能已经被删除，请回退到中长期版本列表");
        }
        LongtermVersionGroupRecordDO latestRecordDO = demandDBHelper.getOne(LongtermVersionGroupRecordDO.class,
                "where version_group_id=? order by id desc", req.getGroupId());
        if (latestRecordDO == null) {
            throw new WrongWebParameterException("分组Id:" + req.getGroupId() + "还没有初始化，请联系开发进行初始化");
        }

        // 2. 检查权限
        WhereSQL dataAuth = approvalService.getDataAuth(false);
        dataAuth.and(" id = ?", req.getGroupId());
        LongtermVersionGroupDO authGroup = demandDBHelper.getOne(LongtermVersionGroupDO.class, dataAuth.getSQL(),
                dataAuth.getParams());
        if (authGroup == null) {
            throw new BizException("您没有权限查看该分组");
        }

        // 3. 处理查看是干预前的数据
        if (req.getIsBeforeIntervene() != null && req.getIsBeforeIntervene()) {
            LongtermVersionGroupStatusEnum statusEnum = LongtermVersionGroupStatusEnum.getByCode(groupDO.getStatus());
            if (LongtermVersionGroupStatusEnum.isOnOrAfterComdIntervene(statusEnum)) {
                // 将record取到COMD_INTERVENE前的最后一个record
                List<LongtermVersionGroupStatusEnum> beforeComdInterveneStatus = LongtermVersionGroupStatusEnum.getBeforeComdInterveneStatus();
                List<String> beforeComdInterveneStatusCode = ListUtils.transform(beforeComdInterveneStatus,
                        LongtermVersionGroupStatusEnum::getCode);
                LongtermVersionGroupRecordDO latestRecordDO2 = demandDBHelper.getOne(LongtermVersionGroupRecordDO.class,
                        "where version_group_id=? and version_group_node_code in (?) order by id desc",
                        req.getGroupId(), beforeComdInterveneStatusCode);
                if (latestRecordDO2 != null) {
                    latestRecordDO = latestRecordDO2;
                }
            }
        }

        // 4. 查询明细并转换
        List<LongtermVersionGroupRecordItemDO> items = demandDBHelper.getAll(
                LongtermVersionGroupRecordItemDO.class,
                "where version_group_record_id=?", latestRecordDO.getId());
        List<LongtermVersionGroupReasonDO> reasons = demandDBHelper.getAll(LongtermVersionGroupReasonDO.class,
                "where version_group_id=?", req.getGroupId());

        QueryLongtermGroupItemResp resp = new QueryLongtermGroupItemResp();
        resp.setVersionCode(versionDO.getVersionCode());
        resp.setTopCustomers(toTopCustomers(groupDO.getTopCustomers()));
        resp.setGroupInfo(LongtermGroupDTO.trans(groupDO, versionDO,
                approvalService.getNodeCodeProcessor(groupDO.getStatus(),groupDO.getIndustryDept(),groupDO.getBizGroup())));
        resp.setVersionRecordId(latestRecordDO.getId());
        resp.setItems(ListUtils.transform(items, LongtermGroupItemDTO::from));
        resp.setReasons(ListUtils.transform(reasons, LongtermGroupReasonDTO::from));

        // 5. 增加时间范围枚举
        resp.setTimes(genTimes(GenTimesReq.buildReq(versionDO), versionDO.getExtraIndustries().contains(groupDO.getIndustryDept())));

        // 6. 处理查询上一期的数据
        if (req.getIsQueryPreviousVersion() != null && req.getIsQueryPreviousVersion()) {
            LongtermVersionDO previousVersionDO = longTermVersionService.getPreviousDoneVersion(
                    groupDO.getVersionCode());
            if (previousVersionDO == null) {
                resp.setPreviousVersionCode("");
            } else {
                // 找到上一期对应的分组id和record id
                LongtermVersionGroupDO previousGroupDO = demandDBHelper.getOne(LongtermVersionGroupDO.class,
                        "where version_code=? and industry_dept=? and product=?", previousVersionDO.getVersionCode(),
                        groupDO.getIndustryDept(), groupDO.getBizGroup());
                if (previousGroupDO == null) {
                    resp.setPreviousVersionCode("");
                } else {
                    LongtermVersionGroupRecordDO previousRecordDO = demandDBHelper.getOne(
                            LongtermVersionGroupRecordDO.class,
                            "where version_group_id=? order by id desc", previousGroupDO.getId());
                    if (previousRecordDO == null) {
                        resp.setPreviousVersionCode("");
                    } else {
                        List<LongtermVersionGroupRecordItemDO> preItems = demandDBHelper.getAll(
                                LongtermVersionGroupRecordItemDO.class,
                                "where version_group_record_id=?", previousRecordDO.getId());
                        resp.setPreviousVersionCode(previousVersionDO.getVersionCode());
                        resp.setPreviousItems(ListUtils.transform(preItems, LongtermGroupItemDTO::from));
                    }
                }
            }
        }

        // 7. 当是驳回状态时，提供驳回原因
        if (LongtermVersionGroupStatusEnum.REJECT.getCode().equals(groupDO.getStatus())) {
            LongtermVersionGroupRecordDO latestRejectRecord = demandDBHelper.getOne(LongtermVersionGroupRecordDO.class,
                    "where version_group_id=? and approve_result like '%驳回%' order by id desc", req.getGroupId());
            if (latestRejectRecord != null) {
                resp.setRejectInfo(LongtermGroupRejectInfoDTO.from(latestRejectRecord));
            }
        }

        // 8.判断当前状态是否可撤回
        if (LongtermVersionGroupStatusEnum.canWithdrawSubmit(groupDO.getStatus())) {
            String nodeCodeProcessor = approvalService.getNodeCodeProcessor(
                    LongtermVersionGroupStatusEnum.CREATE.getCode(), groupDO.getIndustryDept(), groupDO.getBizGroup());
            if (nodeCodeProcessor.contains(LoginUtils.getUserNameWithSystem())) {
                resp.setIsAllowWithdraw(Boolean.TRUE);
            }
        }

        // 9.构建过去两年历史执行的参考数据
        buildLongTermHistoryItem(versionDO, resp);

        return resp;
    }

    private void buildLongTermHistoryItem(LongtermVersionDO versionDO, QueryLongtermGroupItemResp resp) {
        // step 1 给上历史两年的时间
        List<LongtermTimeDTO> historyTimes = new ArrayList<>();
        for (int i = 2; i > 0; i--)  {
            historyTimes.add(new LongtermTimeDTO(versionDO.getDemandBeginYear() - i, 0, 0,
                    LongtermVersionGroupItemTimeUnitEnum.YEAR.getCode(),
                    LongtermVersionGroupItemSourceTypeEnum.HISTORY_SCALE.getCode()));
        }
        resp.setHistoryTimes(historyTimes);
        // step 2 给上历史两年的执行数据
        List<LongtermVersionHistoryItemDO> historyItems = demandDBHelper.getAll(LongtermVersionHistoryItemDO.class,
                "where version_code =?  and version_group_id=?", versionDO.getVersionCode(), resp.getGroupInfo().getGroupId());
        List<LongtermVersionHistoryItemDTO> itemDTOS = ListUtils.transform(historyItems, LongtermVersionHistoryItemDTO::transFromDO);
        resp.setHistoryItems(itemDTOS);
    }

    /**
     * 当第一次编辑时，record的状态是CREATE，此时复制一份数据，并将状态更新为
     *
     * @return 返回新的record
     */
    private LongtermVersionGroupRecordDO copyItemOnFirstSave(LongtermVersionGroupRecordDO latestRecordDO,
            LongtermVersionGroupDO groupDO) {
        if (!LongtermVersionGroupStatusEnum.CREATE.getCode().equals(latestRecordDO.getVersionGroupNodeCode())) {
            return latestRecordDO;
        }
        latestRecordDO.setOperateUser(LoginUtils.getUserNameWithSystem());
        latestRecordDO.setApproveEndTime(new Date());
        latestRecordDO.setApproveResult("");
        latestRecordDO.setApproveNote("切换到录入中");
        demandDBHelper.update(latestRecordDO);

        LongtermVersionGroupRecordDO nextRecordDO = fromPreviousGroupRecord(latestRecordDO,
                LongtermVersionGroupStatusEnum.IN_SUBMIT);
        demandDBHelper.insert(nextRecordDO);

        copyGroupRecordItem(latestRecordDO.getId(), nextRecordDO.getId());

        groupDO.setStatus(LongtermVersionGroupStatusEnum.IN_SUBMIT.getCode());
        demandDBHelper.update(groupDO);

        return nextRecordDO;
    }

    private LongtermVersionGroupRecordDO fromPreviousGroupRecord(LongtermVersionGroupRecordDO previousRecord,
            LongtermVersionGroupStatusEnum nextStatus) {
        LongtermVersionGroupRecordDO record = new LongtermVersionGroupRecordDO();
        record.setVersionCode(previousRecord.getVersionCode());
        record.setVersionGroupId(previousRecord.getVersionGroupId());
        record.setVersionGroupNodeCode(nextStatus.getCode());
        record.setVersionGroupName(nextStatus.getName());
        record.setApproveStartTime(new Date());
        return record;
    }

    private void throwIfEmpty(String column, String columnName) {
        if (Strings.isBlank(column)) {
            throw BizException.makeThrow("字段[%s]参数为空,属于必填项,请检查或联系 fireflychen 处理", columnName);
        }
    }

    private void throwIfNotContain(Collection<String> dict, String column, String columnName) {
        if (Strings.isNotBlank(column) && dict != null && !dict.contains(column)) {
            throw BizException.makeThrow("字段[%s],值[%s]不在策略表中,请检查或请联系 fireflychen 添加配置", columnName,
                    column);
        }
    }

    private void throwIfNotContainByErrorMsg(Collection<String> dict, String column, String errorMsg) {
        if (Strings.isNotBlank(column) && dict != null && !dict.contains(column)) {
            throw BizException.makeThrow(errorMsg);
        }
    }

    private void throwIfNotContainWithUser(Collection<String> dict, String column, String columnName, String userName)  {
        if (Strings.isNotBlank(column) && dict != null && !dict.contains(column)) {
            throw BizException.makeThrow("字段[%s],值[%s]不在策略表中,请检查或请联系 %s 添加配置", columnName,
                    column, userName);
        }
    }

    @Override
    @Transactional("demandTransactionManager")
    public SaveLongtermGroupItemResp saveLongtermGroupItem(SaveLongtermGroupItemReq req) {
        // 1. 查询版本相关信息及校验
        LongtermVersionGroupDO groupDO = demandDBHelper.getOne(LongtermVersionGroupDO.class, "where id=?",
                req.getGroupId());
        if (groupDO == null) {
            throw new WrongWebParameterException(
                    "分组Id:" + req.getGroupId() + "不存在，可能已经被删除，请回退到中长期版本列表");
        }
        boolean ifCanEditHistory = false;
        Map<String, List<String>> controlMap = longtermDictService.queryHistoryControl();
        List<String> control = controlMap.getOrDefault(groupDO.getIndustryDept(), new ArrayList<>());
        if (control.contains(req.getProduct())) {
            ifCanEditHistory = true;
        }
        // 支持操作的数据类型
        List<String> canEdit = new ArrayList<>(Arrays.asList(
                LongtermVersionGroupItemSourceTypeEnum.LONGTERM_INPUT.getCode(),
                LongtermVersionGroupItemSourceTypeEnum.PPL_13_WEEK.getCode(),
                LongtermVersionGroupItemSourceTypeEnum.ORDER.getCode()));
        if (ifCanEditHistory) {
            canEdit.add(LongtermVersionGroupItemSourceTypeEnum.HISTORY_SCALE.getCode());
        }
        checkAndFilterReq(req, canEdit);
        checkItemIsValid(req, groupDO);
        LongtermVersionDO versionDO = demandDBHelper.getOne(LongtermVersionDO.class, "where version_code=?",
                groupDO.getVersionCode());
        if (versionDO == null) {
            throw new WrongWebParameterException(
                    "版本号:" + groupDO.getVersionCode() + "不存在，可能已经被删除，请回退到中长期版本列表");
        }
        LongtermVersionGroupRecordDO latestRecordDO = demandDBHelper.getOne(LongtermVersionGroupRecordDO.class,
                "where version_group_id=? order by id desc", req.getGroupId());
        if (latestRecordDO == null) {
            throw new WrongWebParameterException("分组Id:" + req.getGroupId() + "还没有初始化，请联系开发进行初始化");
        }
        if (!Objects.equals(latestRecordDO.getId(), req.getGroupRecordId())) {
            throw new WrongWebParameterException("分组Id:" + req.getGroupId() + "的记录Id:" + req.getGroupRecordId()
                    + "已不是最新的recordId，不允许修改，请刷新页面后重试");
        }

        // 2. 检查提交人有没有权限
        if (!approvalService.isAllowApprove(groupDO,LoginUtils.getUserNameWithSystem())) {
            throw new BizException("当前用户没有提交权限,如需添加权限,请联系kaijiazhang");
        }

        // 3. 如果状态是CREATE，则转到录入中
        if (LongtermVersionGroupStatusEnum.CREATE.getCode().equals(latestRecordDO.getVersionGroupNodeCode())) {
            latestRecordDO = copyItemOnFirstSave(latestRecordDO, groupDO);
        }

        // 4. 查询数据库最新的items, null 的时候不限制，只查询中长期录入的
        List<LongtermVersionGroupRecordItemDO> dbItems = StringTools.isBlank(req.getProduct()) ?
                demandDBHelper.getAll(LongtermVersionGroupRecordItemDO.class,
                        "where version_group_record_id=? and source_type in (?)",
                        latestRecordDO.getId(), canEdit) :
                demandDBHelper.getAll(LongtermVersionGroupRecordItemDO.class,
                        "where version_group_record_id=? and product=? and source_type in (?)",
                        latestRecordDO.getId(),
                        req.getProduct(),
                        canEdit);

        // 5. 匹配后进行CRUD
        List<String> topCustomers = toTopCustomers(groupDO.getTopCustomers());
        syncItemsToDB(dbItems, canEdit, req.getItems(), topCustomers, latestRecordDO);

        SaveLongtermGroupItemResp resp = new SaveLongtermGroupItemResp();
        resp.setSuccess(true);
        resp.setErrMsg("");

        return resp;
    }

    private static void checkAndFilterReq(SaveLongtermGroupItemReq req, List<String> canEdit) {
        if (req == null || req.getGroupId() == null) {
            throw new WrongWebParameterException("缺少参数groupId");
        }
        if (req.getGroupRecordId() == null) {
            throw new WrongWebParameterException("缺少参数groupRecordId");
        }
        req.getItems().removeIf(o -> !canEdit.contains(o.getSourceType()));
        if (ListUtils.isEmpty(req.getItems())) {
            if (Strings.isNotBlank(req.getProduct())) {
                if (!req.getIsDelAll()) {
                    throw BizException.makeThrow(
                            "当前保存%s产品的全量数据, 不存在%s产品的数据，请检查数据或联系kaijiazhang处理",
                            req.getProduct(), req.getProduct());
                }
            } else {
                if (!req.getIsDelAll()) {
                    throw BizException.makeThrow("上传的数据为空，请检查数据或者联系kaijiazhang处理");
                }
            }

        }
    }

    private void checkItemIsValid(SaveLongtermGroupItemReq req, LongtermVersionGroupDO groupDO) {
        List<String> customhouseTitleDict = Lang.list("境内", "境外");
        List<String> demandTypeDict = ListUtils.transform(PplDemandTypeEnum.values(), PplDemandTypeEnum::getCode);
        Map<String, List<String>> regionInfoMap = longtermDictService.queryRegionNameToZoneName();
        Map<String, List<String>> customhouseTitleToRegionName = longtermDictService.getCustomhouseTitleToRegionName(regionInfoMap.keySet());
        Map<String, List<String>> instanceInfoMap = longtermDictService.queryInstanceFamilyToInstanceType();
        List<String> gpuTypes = longtermDictService.queryGpuType();
        Set<String> topCustomers = groupDO.parseTopCustomersList();

        // 2. 检查下前端请求的数据
        req.getItems().forEach(o -> {

            boolean productNotBlank = Strings.isNotBlank(o.getProduct());
            boolean productNotGpu = !Strings.equals(o.getProduct(), Ppl13weekProductTypeEnum.GPU.getName());
            boolean gpuTypeNotBlank = Strings.isNotBlank(o.getGpuType());
            throwIfEmpty(o.getProduct(), "产品");
            throwIfEmpty(o.getCustomhouseTitle(), "境内外");
            throwIfEmpty(o.getCustomerShortName(), "客户简称");
            throwIfEmpty(o.getRegionName(), "地域");
            if (productNotGpu) {
                throwIfEmpty(o.getInstanceFamily(), "实例族");
            }
            boolean isTopCustomer = topCustomers.contains(o.getCustomerShortName());
            if (isTopCustomer) {
                throwIfEmpty(o.getZoneName(), "大客户的可用区");
                if (productNotGpu) {
                    throwIfEmpty(o.getInstanceType(), "大客户的实例类型");
                }
            }
            throwIfEmpty(o.getDemandType(), "需求类型");
            if (!productNotGpu) {
                throwIfEmpty(o.getGpuType(), "GPU卡型");
                throwIfNotContainWithUser(gpuTypes, o.getGpuType(), "GPU卡型", "GPU产品计划员:keithyu");
            }

            throwIfNotContain(customhouseTitleDict, o.getCustomhouseTitle(), "境内外");
            if (!Strings.isBlank(o.getRegionName())) {
                if (Strings.equals("境外", o.getCustomhouseTitle())) {
                    throwIfNotContainByErrorMsg(customhouseTitleToRegionName.getOrDefault(o.getCustomhouseTitle(), new ArrayList<>()),
                            o.getRegionName(),
                            String.format("境内外【%s】不存在地域【%s】", o.getCustomhouseTitle(), o.getRegionName()));
                }else {
                    Set<String> regionSet = new HashSet<>(customhouseTitleToRegionName.getOrDefault(o.getCustomhouseTitle(), new ArrayList<>()));
                    regionSet.add("随机地域");
                    throwIfNotContainByErrorMsg(regionSet, o.getRegionName(),
                            String.format("境内外【%s】不存在地域【%s】", o.getCustomhouseTitle(), o.getRegionName()));
                }
                List<String> zoneNames = regionInfoMap.getOrDefault(o.getRegionName(),new ArrayList<>());
                zoneNames.add("随机可用区");
                throwIfNotContain(zoneNames, o.getZoneName(), "可用区名称");
            }
            if (!Strings.isBlank(o.getInstanceFamily())) {
                throwIfNotContain(instanceInfoMap.keySet(), o.getInstanceFamily(), "实例族");
                List<String> instanceTypes = instanceInfoMap.get(o.getInstanceFamily());
                throwIfNotContain(instanceTypes, o.getInstanceType(), "实例类型");
            }
            throwIfNotContain(demandTypeDict, o.getDemandType(), "需求类型");

            if (productNotBlank && productNotGpu && gpuTypeNotBlank) {
                String format = "录入产品：%s中,不属于GPU，存在非空GPU卡型，GPU卡型请保持为空";
                throw BizException.makeThrow(format, o.getProduct());
            }
            if (Objects.equals(o.getTimeUnit(), LongtermVersionGroupItemTimeUnitEnum.QUARTER.getCode())) {
                o.setDemandMonth(0);
            }
        });
    }

    @Override
    @Transactional("demandTransactionManager")
    public ReplaceIntervened13weekResp replaceIntervened13week(ReplaceIntervened13weekReq req) {
        // 1. 查询版本相关信息及校验
        LongtermVersionGroupDO groupDO = demandDBHelper.getOne(LongtermVersionGroupDO.class, "where id=?",
                req.getGroupId());
        if (groupDO == null) {
            throw new WrongWebParameterException(
                    "分组Id:" + req.getGroupId() + "不存在，可能已经被删除，请回退到中长期版本列表");
        }
        LongtermVersionDO versionDO = demandDBHelper.getOne(LongtermVersionDO.class, "where version_code=?",
                groupDO.getVersionCode());
        if (versionDO == null) {
            throw new WrongWebParameterException(
                    "版本号:" + groupDO.getVersionCode() + "不存在，可能已经被删除，请回退到中长期版本列表");
        }
        LongtermVersionGroupRecordDO latestRecordDO = demandDBHelper.getOne(LongtermVersionGroupRecordDO.class,
                "where version_group_id=? order by id desc", req.getGroupId());
        if (latestRecordDO == null) {
            throw new WrongWebParameterException("分组Id:" + req.getGroupId() + "还没有初始化，请联系开发进行初始化");
        }
        if (!Objects.equals(latestRecordDO.getId(), req.getGroupRecordId())) {
            throw new WrongWebParameterException("分组Id:" + req.getGroupId() + "的记录Id:" + req.getGroupRecordId()
                    + "已不是最新的recordId，不允许修改，请刷新页面后重试");
        }

        if (StringTools.isBlank(versionDO.getPpl13weekVersionCode())) {
            return ReplaceIntervened13weekResp.fail("当前版本未配置13周版本号");
        }
        YearMonth versionStartYearMonth = versionDO.parsePpl13weekStartYearMonth();
        if (versionStartYearMonth == null) {
            return ReplaceIntervened13weekResp.fail("当前版本未配置13周开始月份");
        }
        YearMonth ppl13WeekEndYearMonth = versionDO.parsePpl13weekEndYearMonth();
        if (ppl13WeekEndYearMonth == null) {
            return ReplaceIntervened13weekResp.fail("当前版本未配置13周结束月份");
        }

        // 2. 查询13周的干预后的数据
        List<LongtermPpl13weekDataDTO> comdIntervenePpl13WeekData = getComdIntervenePpl13WeekData(versionDO, groupDO,
                req.getIsUseIntervenedData());

        // 3. 替换当前record对应13周月份的数据
        List<LongtermVersionGroupRecordItemDO> ppl13weekData = demandDBHelper.getAll(
                LongtermVersionGroupRecordItemDO.class,
                "where version_group_record_id=? and (demand_year>? or demand_year=? and demand_month>=?) and (demand_year<? or demand_year=? and demand_month<=?)",
                latestRecordDO.getId(),
                versionStartYearMonth.getYear(), versionStartYearMonth.getYear(),
                versionStartYearMonth.getMonthValue(),
                ppl13WeekEndYearMonth.getYear(), ppl13WeekEndYearMonth.getYear(),
                ppl13WeekEndYearMonth.getMonthValue());

        List<LongtermVersionGroupRecordItemDO> comdIntervend = ListUtils.transform(comdIntervenePpl13WeekData,
                o -> LongtermPpl13weekDataDTO.toDO(o, latestRecordDO, true, versionDO.parsePpl13weekStartYearMonth()));

        demandDBHelper.delete(ppl13weekData);
        demandDBHelper.insertBatchWithoutReturnId(comdIntervend);

        // 4. 记录干预操作到数据库
        groupDO.setComdIntervened13week(req.getIsUseIntervenedData() ? 1 : 2);
        groupDO.setComdIntervenedTime(LocalDateTime.now());
        demandDBHelper.update(groupDO);

        return ReplaceIntervened13weekResp.success();
    }

    private List<String> getTopCustomers(LongtermVersionGroupDO group) {
        Set<String> result = new HashSet<>();
        if (StringTools.isNotBlank(group.getTopCustomers())) {
            result.addAll(ListUtils.transform(group.getTopCustomers().split(","), o -> o));
        }
        return ListUtils.transform(ListUtils.filter(result, StringTools::isNotBlank), String::trim);
    }

    private List<LongtermPpl13weekDataDTO> getComdIntervenePpl13WeekData(LongtermVersionDO versionDO,
            LongtermVersionGroupDO groupDO, boolean isUseIntervenedData) {

        List<String> topCustomers = getTopCustomers(groupDO);

        List<PplConfigProductEnumDO> products = demandDBHelper.getAll(PplConfigProductEnumDO.class);
        List<PplConfigProductEnumDO> paasProducts = ListUtils.filter(products,
                product -> "PAAS".equals(product.getFlag()));
        List<PplConfigProductEnumDO> nonPaasProducts = ListUtils.filter(products,
                product -> !"PAAS".equals(product.getFlag()));

        String sql = ORMUtils.getSql("/sql/longterm/get_ppl_13week_data_intervened.sql");
        if (isUseIntervenedData) {
            sql = sql.replace("${INTERVENE_CONDITION}", "and source in ('IMPORT','COMD_INTERVENE') and is_comd!=1"); // 干预后
        } else {
            sql = sql.replace("${INTERVENE_CONDITION}", "and source in ('IMPORT')"); // 干预前
        }
        Map<String, Object> params = new HashMap<>();

        if (StringTools.isBlank(versionDO.getPpl13weekVersionCode())) {
            // 没有绑定13周，不处理13周数据
            log.warn("longterm version code {} not bind 13week ppl, will not handle", versionDO.getVersionCode());
            return new ArrayList<>();
        }
        params.put("versionCode", versionDO.getPpl13weekVersionCode());
        YearMonth versionStartYearMonth = versionDO.parseVersionStartYearMonth();
        if (versionStartYearMonth == null) {
            // 开始年月不存在则不处理
            log.error("longterm version {} 13week ppl start year month is null, will not hanlde",
                    versionDO.getVersionCode());
            return new ArrayList<>();
        }
        params.put("beginYear", versionStartYearMonth.getYear());
        params.put("beginMonth", versionStartYearMonth.getMonthValue());
        YearMonth ppl13WeekEndYearMonth = versionDO.parsePpl13weekEndYearMonth();
        if (ppl13WeekEndYearMonth == null) {
            // 13周结束年月不存在则不处理
            log.error("longterm version {} 13week ppl end year month is null, will not hanlde",
                    versionDO.getVersionCode());
            return new ArrayList<>();
        }
        params.put("endYear", ppl13WeekEndYearMonth.getYear());
        params.put("endMonth", ppl13WeekEndYearMonth.getMonthValue());
        params.put("topCustomers", topCustomers);
        params.put("paasProduct", ListUtils.transform(paasProducts, PplConfigProductEnumDO::getProductName));
        params.put("nonPaasProduct", ListUtils.transform(nonPaasProducts, PplConfigProductEnumDO::getProductName));

        sql = sql.replace("${INSTANCE_TYPE_2_INSTANCE_FAMILY_CASE_WHEN}", getInstanceTypeToInstanceFamilyCaseWhen());

        params.put("industryDept", groupDO.getIndustryDept());
        params.put("bizGroup", groupDO.getBizGroup() == null ? "" : groupDO.getBizGroup());

        return ckcldStdCrpDBHelper.getRaw(LongtermPpl13weekDataDTO.class, sql, params);
    }

    private String getInstanceTypeToInstanceFamilyCaseWhen() {
        List<Map> list = cdCommonDbHelper.getRaw(Map.class,
                "select distinct a.ginsfamily,b.ginskingdom_name\n" +
                        "from static_ginstype a left join static_ginsfamily b on a.ginsphylum=b.ginsphylum");
        Map<String, List<String>> instanceFamily2InstanceType = ListUtils.toMapList(list,
                o -> o.get("ginskingdom_name").toString(), o -> o.get("ginsfamily").toString());

        StringBuilder sb = new StringBuilder();
        if (MapUtils.isEmpty(instanceFamily2InstanceType)) {
            return "instance_type";
        } else {
            sb.append("(case ");
            for (Map.Entry<String, List<String>> entry : instanceFamily2InstanceType.entrySet()) {
                sb.append(" when instance_type in (");
                sb.append(StringTools.join(ListUtils.transform(entry.getValue(), o -> "'" + o.trim() + "'"), ","));
                sb.append(" ) then '").append(entry.getKey()).append("' ");
            }
            sb.append(" else instance_type end)");
        }
        return sb.toString();
    }

    @Override
    public SaveLongtermGroupReasonResp saveLongtermGroupReason(SaveLongtermGroupReasonReq req) {
        // 1. 查询版本相关信息及校验
        LongtermVersionGroupDO groupDO = demandDBHelper.getOne(LongtermVersionGroupDO.class, "where id=?",
                req.getGroupId());
        if (groupDO == null) {
            throw new WrongWebParameterException(
                    "分组Id:" + req.getGroupId() + "不存在，可能已经被删除，请回退到中长期版本列表");
        }
        LongtermVersionDO versionDO = demandDBHelper.getOne(LongtermVersionDO.class, "where version_code=?",
                groupDO.getVersionCode());
        if (versionDO == null) {
            throw new WrongWebParameterException(
                    "版本号:" + groupDO.getVersionCode() + "不存在，可能已经被删除，请回退到中长期版本列表");
        }
        LongtermVersionGroupRecordDO latestRecordDO = demandDBHelper.getOne(LongtermVersionGroupRecordDO.class,
                "where version_group_id=? order by id desc", req.getGroupId());
        if (latestRecordDO == null) {
            throw new WrongWebParameterException("分组Id:" + req.getGroupId() + "还没有初始化，请联系开发进行初始化");
        }
        if (!Objects.equals(latestRecordDO.getId(), req.getGroupRecordId())) {
            throw new WrongWebParameterException("分组Id:" + req.getGroupId() + "的记录Id:" + req.getGroupRecordId()
                    + "已不是最新的recordId，不允许修改，请刷新页面后重试");
        }

        // 2. 检查权限
        if (!approvalService.isAllowApprove(groupDO,LoginUtils.getUserNameWithSystem())) {
            throw new BizException("当前用户没有提交权限,如需添加权限,请联系kaijiazhang");
        }

        // 3. 保存
        WhereSQL whereSQL = new WhereSQL();
        if (StringTools.isNotBlank(req.getProduct())) {
            whereSQL.and("product=?", req.getProduct());
        }
        whereSQL.and("version_group_id=?", req.getGroupId());
        List<LongtermVersionGroupReasonDO> dbReasons = demandDBHelper.getAll(LongtermVersionGroupReasonDO.class,
                whereSQL.getSQL(), whereSQL.getParams());
        syncReasonsToDB(dbReasons, req.getReasons(), latestRecordDO);

        SaveLongtermGroupReasonResp resp = new SaveLongtermGroupReasonResp();
        resp.setSuccess(true);
        resp.setErrMsg("");
        return resp;
    }

    @Override
    public void copyGroupRecordItem(Long oldRecordId, Long newRecordId) {
        List<LongtermVersionGroupRecordItemDO> items = demandDBHelper.getAll(
                LongtermVersionGroupRecordItemDO.class,
                "where version_group_record_id=?", oldRecordId);
        items.forEach(o -> {
            o.setId(null);
            o.setVersionGroupRecordId(newRecordId);
        });
        demandDBHelper.insertBatchWithoutReturnId(items);
    }

    private void syncReasonsToDB(List<LongtermVersionGroupReasonDO> dbReasons,
            List<LongtermGroupReasonDTO> reasons, LongtermVersionGroupRecordDO latestRecordDO) {
        Map<String, List<LongtermVersionGroupReasonDO>> dbReasonMap = ListUtils.toMapList(dbReasons,
                LongtermReasonDimGK::groupKey, o -> o);
        Map<String, List<LongtermGroupReasonDTO>> reasonMap = ListUtils.toMapList(reasons,
                LongtermReasonDimGK::groupKey, o -> o);

        dbReasonMap.keySet().forEach(key -> {
            if (!reasonMap.containsKey(key)) {
                dbReasonMap.get(key).forEach(o -> o.setDeleted(true));
            }
        });
        reasonMap.keySet().forEach(key -> {
            List<LongtermGroupReasonDTO> newItem = reasonMap.get(key);
            if (newItem.size() > 1) {
                throw new WrongWebParameterException("出现重复维度的数据，请根据维度数值进行修改：" + key);
            }
            LongtermGroupReasonDTO item = newItem.get(0);

            List<LongtermVersionGroupReasonDO> dbItemDOs = dbReasonMap.get(key);
            if (ListUtils.isEmpty(dbItemDOs)) {
                LongtermVersionGroupReasonDO itemDO = LongtermGroupReasonDTO.toReasonDO(item);
                itemDO.setVersionGroupId(latestRecordDO.getVersionGroupId());
                itemDO.setVersionCode(latestRecordDO.getVersionCode());
                dbReasons.add(itemDO);
            } else {
                if (dbItemDOs.size() > 1) {
                    throw new WrongWebParameterException(
                            "数据库中明细数据出现重复维度的数据，请联系开发处理，维度数值：" + key);
                }
                LongtermVersionGroupReasonDO dbItemDO = dbItemDOs.get(0);
                dbItemDO.setReason(item.getReason());
            }
        });

        demandDBHelper.insertOrUpdate(dbReasons);
    }

    private void syncItemsToDB(List<LongtermVersionGroupRecordItemDO> dbItems, List<String> canEdit,
                               List<LongtermGroupItemDTO> items,
                               List<String> topCustomers, LongtermVersionGroupRecordDO latestRecordDO) {

        Map<String, List<LongtermVersionGroupRecordItemDO>> dbItemMap = ListUtils.toMapList(dbItems,
                LongtermItemBizTimeDimGK::groupKey, o -> o);
        Map<String, List<LongtermGroupItemDTO>> itemMap = ListUtils.toMapList(items, LongtermItemBizTimeDimGK::groupKey,
                o -> o);

        dbItemMap.keySet().forEach(key -> {
            if (!itemMap.containsKey(key)) {
                if (canEdit.contains(dbItemMap.get(key).get(0).getSourceType())) {
                    dbItemMap.get(key).forEach(o -> o.setDeleted(true));
                }
            }
        });
        itemMap.keySet().forEach(key -> {
            List<LongtermGroupItemDTO> newItem = itemMap.get(key);
            if (newItem.size() > 1) {
                throw new WrongWebParameterException("出现重复维度的数据，请根据维度数值进行修改：" + key);
            }
            LongtermGroupItemDTO item = newItem.get(0);

            List<LongtermVersionGroupRecordItemDO> dbItemDOs = dbItemMap.get(key);
            if (ListUtils.isEmpty(dbItemDOs)) {
                LongtermVersionGroupRecordItemDO itemDO = LongtermGroupItemDTO.toRecordItemDO(item);
                itemDO.setVersionGroupRecordId(latestRecordDO.getId());
                itemDO.setVersionGroupId(latestRecordDO.getVersionGroupId());
                itemDO.setVersionCode(latestRecordDO.getVersionCode());
                if (canEdit.contains(itemDO.getSourceType())) {
                    dbItems.add(itemDO);
                }
            } else {
                if (dbItemDOs.size() > 1) {
                    throw new WrongWebParameterException(
                            "数据库中明细数据出现重复维度的数据，请联系开发处理，维度数值：" + key);
                }
                LongtermVersionGroupRecordItemDO dbItemDO = dbItemDOs.get(0);
                if (canEdit.contains(dbItemDO.getSourceType())) {
                    dbItemDO.setNote(item.getNote());
                    dbItemDO.setGpuNum(item.getGpuNum());
                    dbItemDO.setCoreNum(item.getCoreNum());
                }
            }
        });
        // 重刷下isTopCustomer字段
        // 所有插入db 的都是正数
        dbItems.forEach(o ->
        {
            o.setIsTopCustomer(topCustomers.contains(o.getCustomerShortName()) ? 1 : 0);
            if (o.getCoreNum() != null) {
                o.setCoreNum(o.getCoreNum().abs());
            }
            if (o.getGpuNum() != null) {
                o.setGpuNum(o.getGpuNum().abs());
            }
        });
        demandDBHelper.insertOrUpdate(dbItems);
    }

    private List<String> toTopCustomers(String topCustomers) {
        if (StringTools.isBlank(topCustomers)) {
            return new ArrayList<>();
        }
        String[] split = topCustomers.split(",");
        List<String> result = new ArrayList<>();
        for (String s : split) {
            if (StringTools.isNotBlank(s)) {
                result.add(s.trim());
            }
        }
        return result;
    }

    public List<LongtermTimeDTO> genTimes(GenTimesReq req, boolean if35)  {
        YearMonth demandEndYearMonth = YearMonth.of(req.getDemandEndYear(), req.getDemandEndMonth());
        YearMonth ppl13weekBegYearMonth = req.parsePpl13weekBegYearMonth();
        YearMonth ppl13weekEndYearMonth = req.parsePpl13weekEndYearMonth();
        YearMonth versionYm = YearMonth.parse(req.getVersionYearMonth());

        List<LongtermTimeDTO> times = new ArrayList<>();
        YearMonth cur = YearMonth.of(req.getDemandBeginYear(), req.getDemandBeginMonth());
        int current13WeekQuarter = 0;
        while (!cur.isAfter(demandEndYearMonth)) {

            int curQuarter = (cur.getMonthValue() + 2) / 3;

            if (cur.isBefore(versionYm)) {
                // 如果比当前版本年月小，是历史执行
                times.add(new LongtermTimeDTO(cur.getYear(), curQuarter, cur.getMonthValue(),
                        LongtermVersionGroupItemTimeUnitEnum.MONTH.getCode(),
                        LongtermVersionGroupItemSourceTypeEnum.HISTORY_SCALE.getCode()));
                cur = cur.plusMonths(1);
                continue;
            }

            if (!cur.isBefore(versionYm) && cur.isBefore(ppl13weekBegYearMonth)) {
                // 如果大于等于当前版本年月，且小于13周，是订单数据
                times.add(new LongtermTimeDTO(cur.getYear(), curQuarter, cur.getMonthValue(),
                        LongtermVersionGroupItemTimeUnitEnum.MONTH.getCode(),
                        LongtermVersionGroupItemSourceTypeEnum.ORDER.getCode()));
                cur = cur.plusMonths(1);
                continue;
            }

            boolean isIn13Week = ppl13weekEndYearMonth != null && !cur.isAfter(ppl13weekEndYearMonth);
            current13WeekQuarter = isIn13Week ? curQuarter
                    : (current13WeekQuarter == curQuarter ? current13WeekQuarter : 0); // 延续同一个季节
            boolean isByMonth = true;
            if (isIn13Week) {
                times.add(new LongtermTimeDTO(cur.getYear(), curQuarter, cur.getMonthValue(),
                        LongtermVersionGroupItemTimeUnitEnum.MONTH.getCode(),
                        LongtermVersionGroupItemSourceTypeEnum.PPL_13_WEEK.getCode()));
            } else {
                if (current13WeekQuarter == curQuarter) {
                    times.add(new LongtermTimeDTO(cur.getYear(), curQuarter, cur.getMonthValue(),
                            LongtermVersionGroupItemTimeUnitEnum.MONTH.getCode(),
                            LongtermVersionGroupItemSourceTypeEnum.LONGTERM_INPUT.getCode()));
                } else {
                    times.add(new LongtermTimeDTO(cur.getYear(), curQuarter, 0,
                            LongtermVersionGroupItemTimeUnitEnum.QUARTER.getCode(),
                            LongtermVersionGroupItemSourceTypeEnum.LONGTERM_INPUT.getCode()));
                    isByMonth = false;
                }
            }
            cur = cur.plusMonths(isByMonth ? 1 : 3);
        }

        // 拼装35年范围
        if (if35) {
            YearMonth beg35YearMonth = YearMonth.of(req.getExtraBeginYear(), req.getExtraBeginMonth());
            YearMonth end35YearMonth = YearMonth.of(req.getExtraEndYear(), req.getExtraEndMonth());
            while (beg35YearMonth.isBefore(end35YearMonth)) {
                times.add(new LongtermTimeDTO(beg35YearMonth.getYear(), 0, 0,
                        LongtermVersionGroupItemTimeUnitEnum.YEAR.getCode(),
                        LongtermVersionGroupItemSourceTypeEnum.LONGTERM_INPUT.getCode()));
                beg35YearMonth = beg35YearMonth.plusYears(1);
            }
        }

        return times;
    }
}
