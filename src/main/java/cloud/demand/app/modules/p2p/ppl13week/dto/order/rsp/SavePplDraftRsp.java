package cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDraftDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDraftDO;
import java.util.List;
import lombok.Data;

@Data
public class SavePplDraftRsp {

    String pplOrder;
    List<String> pplIds;

    List<PplItemDraftDO> pplItemDraftDOList;

    PplOrderDraftDO orderDraftDO;
}
