package cloud.demand.app.modules.p2p.ppl13week.service.filler.handler;

import cloud.demand.app.common.utils.StdUtils;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplConfigStatIndustryDeptClassDO;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.CategoryFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerHandler;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class CategoryFillerHandler implements FillerHandler<CategoryFiller>  {

    @Resource
    private DBHelper demandDBHelper;

    @Override
    public void fill(List<CategoryFiller> obj) {
        List<PplConfigStatIndustryDeptClassDO> configs = demandDBHelper.getAll(PplConfigStatIndustryDeptClassDO.class);
        Map<String, String> configMap = ListUtils.toMap(configs, PplConfigStatIndustryDeptClassDO::getIndustryDept,
                PplConfigStatIndustryDeptClassDO::getCategory);
        obj.forEach(data->{
            if (data == null) {
                return;
            }
            String category = configMap.get(data.provideIndustryDept());
            data.fillCategory(StdUtils.handleStr(category));
        });
    }
}
