package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO;

import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastInputExcludedSpikeDO;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SpikeDTO {

    @Column(value = "year")
    private Integer year;

    @Column(value = "month")
    private Integer month;

    /** 行业部门 */
    @Column(value = "industry_dept")
    private String industryDept;

    @Column(value = "customer_uin")
    private String customerUin;

    @Column(value = "customer_short_name")
    private String customerShortName;

    @Column(value = "customhouse_title")
    private String customhouseTitle;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "instance_type")
    private String instanceType;

    @Column(value = "biz_range_type")
    private String bizRangeType;

    @Column(value = "new_core")
    private BigDecimal newCore;

    @Column(value = "ret_core")
    private BigDecimal retCore;

    @Column(value = "cur_core")
    private BigDecimal curCore;

    public PplForecastInputExcludedSpikeDO toSpikeDO() {
        PplForecastInputExcludedSpikeDO pplForecastInputExcludedSpikeDO = new PplForecastInputExcludedSpikeDO();
        pplForecastInputExcludedSpikeDO.setYear(this.getYear());
        pplForecastInputExcludedSpikeDO.setMonth(this.getMonth());
        pplForecastInputExcludedSpikeDO.setIndustryDept(this.getIndustryDept());
        pplForecastInputExcludedSpikeDO.setCustomerUin(this.getCustomerUin());
        pplForecastInputExcludedSpikeDO.setCustomerShortName(this.getCustomerShortName());
        pplForecastInputExcludedSpikeDO.setInstanceType(this.getInstanceType());
        pplForecastInputExcludedSpikeDO.setRegionName(this.getRegionName());
        pplForecastInputExcludedSpikeDO.setZoneName(this.getZoneName());
        pplForecastInputExcludedSpikeDO.setNewCore(this.getNewCore());
        pplForecastInputExcludedSpikeDO.setRetCore(this.getRetCore());
        return pplForecastInputExcludedSpikeDO;
    }

}
