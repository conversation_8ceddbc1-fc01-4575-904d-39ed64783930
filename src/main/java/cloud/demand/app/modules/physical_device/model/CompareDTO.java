package cloud.demand.app.modules.physical_device.model;


import cloud.demand.app.modules.physical_device.entity.CvmHedgeResultDO;
import cloud.demand.app.modules.physical_device.entity.ResPlanDO;
import lombok.Data;

@Data
public class CompareDTO {
    private String yearMonthWeek;
    private String yearMonth;
    private Integer week;
    private String cvmDeptName;
    private String cvmPlanProductName;
    private String deviceType;
    private String campusName;
    private String cloudRegionName;
    private String country;
    private String deviceTypeFuzzy;
    private String regionFuzzy;
    private Integer gapNum;
    private Integer newPhyNum;
    private Integer phyNum;
    private Integer cvmNum;

    public static CompareDTO copy(CompareDTO o) {
        CompareDTO compareDTO = new CompareDTO();
        compareDTO.setYearMonthWeek(o.getYearMonthWeek());
        compareDTO.setYearMonth(o.getYearMonth());
        compareDTO.setWeek(o.getWeek());
        compareDTO.setCvmDeptName(o.getCvmDeptName());
        compareDTO.setCvmPlanProductName(o.getCvmPlanProductName());
        compareDTO.setDeviceType(o.getDeviceType());
        compareDTO.setCampusName(o.getCampusName());
        compareDTO.setCloudRegionName(o.getCloudRegionName());
        compareDTO.setCountry(o.getCountry());
        compareDTO.setDeviceTypeFuzzy(o.getDeviceTypeFuzzy());
        compareDTO.setRegionFuzzy(o.getRegionFuzzy());
        compareDTO.setGapNum(o.getGapNum());
        compareDTO.setNewPhyNum(o.getNewPhyNum());
        compareDTO.setPhyNum(o.getPhyNum());
        compareDTO.setCvmNum(o.getCvmNum());
        return compareDTO;
    }

    public static CompareDTO copy(CvmHedgeResultDO o) {
        CompareDTO compareDTO = new CompareDTO();
        compareDTO.setYearMonthWeek(o.getYearMonth() + "-" + o.getDemandWeek());
        compareDTO.setYearMonth(o.getYearMonth());
        compareDTO.setWeek(o.getDemandWeek());
        compareDTO.setCvmDeptName(o.getCvmDeptName());
        compareDTO.setCvmPlanProductName(o.getCvmPlanProductName());
        compareDTO.setDeviceType(o.getDeviceType());
        compareDTO.setCampusName(o.getCampusName());
        compareDTO.setCloudRegionName(o.getCity());
        compareDTO.setCountry(o.getCountry());
        compareDTO.setDeviceTypeFuzzy(o.getDeviceTypeFuzzy());
        compareDTO.setRegionFuzzy(o.getRegionFuzzy());
        return compareDTO;

    }

    public static CompareDTO copy(ResPlanDO o) {
        CompareDTO compareDTO = new CompareDTO();
        compareDTO.setYearMonthWeek(o.getYearMonth() + "-" + o.getWeek());
        compareDTO.setYearMonth(o.getYearMonth());
        compareDTO.setWeek(o.getWeek());
        compareDTO.setCvmDeptName(o.getCvmSourceDeptName());
        compareDTO.setCvmPlanProductName(o.getCvmSourcePlanProductName());
        compareDTO.setDeviceType(o.getDeviceType());
        compareDTO.setCampusName(o.getCampus());
        compareDTO.setCloudRegionName(o.getCityName());
        compareDTO.setCountry(o.getCountry());
        compareDTO.setDeviceTypeFuzzy(o.getDeviceTypeFuzzy());
        compareDTO.setRegionFuzzy(o.getRegionFuzzy());
        return compareDTO;
    }
}
