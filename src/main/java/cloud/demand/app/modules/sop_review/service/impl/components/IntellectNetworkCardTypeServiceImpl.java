package cloud.demand.app.modules.sop_review.service.impl.components;

import cloud.demand.app.entity.resource.BasStrategeDeviceTypeDO;
import cloud.demand.app.modules.erp_transfer_return.service.ErpBaseService;
import cloud.demand.app.modules.erp_transfer_return.service.impl.ErpBaseServiceImpl;
import cloud.demand.app.modules.sop_review.model.clean.ICleanCoreComponentsBit;
import cloud.demand.app.modules.sop_review.service.ComponentsFieldService;
import cloud.demand.app.modules.sop_review.service.SopReviewCleanService;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/** 智能网络 */
@Service(value = "intellectNetworkCardTypeService")
public class IntellectNetworkCardTypeServiceImpl implements ComponentsFieldService {

    @Resource
    private ErpBaseService erpBaseService;

    @Override
    public List<String> getEnumValues() {
        Map<String, BasStrategeDeviceTypeDO> basStrategeDeviceTypeMap = erpBaseService.getBasStrategeDeviceTypeMap();
        return basStrategeDeviceTypeMap.values().stream().map(BasStrategeDeviceTypeDO::getIntellectNetworkCardType).distinct()
                .collect(
                        Collectors.toList());
    }

    @Override
    public boolean hasEnumValues() {
        return true;
    }

    @Override
    public String[] getEnumValue(ICleanCoreComponentsBit bit) {
        return new String[]{bit.getIntellectNetworkCardType()};
    }
}
