package cloud.demand.app.modules.sop_return.utils;

import cloud.demand.app.modules.sop.util.CkDBUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 *  清洗构建类，通过 update set xxx = case when end 的方式
 *  可以清洗字段
 *  deviceType -> generationType
 *  cvmInstanceModel -> coreType
 *  planProductName [computeType] -> class_2(big_class),class_3(product_class)
 *  需要上游给的字段
 *  assetId  固定资产编号
 *  returnReasonType 退回原因分类
 *  returnTag    退回原始标签（已执行才有）
 *  oriReturnTag 退回标签（已执行才有）
 **/
public class SopCaseBuilder {

    public static String updateSql(Class<?> tClass,String where, String... setSql){
        StringBuilder sbr = new StringBuilder();
        for (String set : setSql) {
            sbr.append(set);
            sbr.append(",");
        }
        sbr.setLength(sbr.length()-1);
        sbr.append(" ").append(where);
        return CkDBUtils.update(tClass,sbr.toString());
    }

    public static String build(String source,String target,Map<Object,String> map){
        if (CollectionUtils.isEmpty(map)){
            return target + " = " + target;
        }
        StringBuilder sbr = new StringBuilder();
        sbr.append(target).append(" = (case");
        for (Map.Entry<Object, String> entry : map.entrySet()) {
            Object key = entry.getKey();
            String value = entry.getValue();
            sbr.append(" when ").append(source);
            if (key instanceof Object[]){
                sbr.append(" in ('").append(StringUtils.join((Object[])key,"','")).append("')");
            }else {
                sbr.append(" = '").append(key).append("'");
            }
            sbr.append(" then ");
            if (StringUtils.isBlank(value)){
                sbr.append(target);
            }else {
                sbr.append("'").append(value).append("'");
            }
        }
        sbr.append(" else ").append(target).append(" end)");
        return sbr.toString();
    }

    public static String build2(String source,String target,Map<?,String> map){
        if (CollectionUtils.isEmpty(map)){
            return "'(空值)'";
        }
        StringBuilder sbr = new StringBuilder();
        sbr.append("(case");
        for (Map.Entry<?, String> entry : map.entrySet()) {
            Object key = entry.getKey();
            String value = entry.getValue();
            sbr.append(" when ").append(source);
            if (key instanceof Object[]){
                sbr.append(" in ('").append(StringUtils.join((Object[])key,"','")).append("')");
            }else if (key instanceof List){
                sbr.append(" in ('").append(StringUtils.join(((List<?>) key).toArray(),"','")).append("')");
            }else {
                sbr.append(" = '").append(key).append("'");
            }
            sbr.append(" then ");
            if (StringUtils.isBlank(value)){
                sbr.append("'(空值)'");
            }else {
                sbr.append("'").append(value).append("'");
            }
        }
        sbr.append(" else ").append("'(空值)'").append(" end)");
        return sbr.toString();
    }

    public static <T> String build(String[] sources, String[] target, Map<Object,T> map, Function<T,String>[] fieldFunArr){
        if (CollectionUtils.isEmpty(map)){
            return target[0] + " = " + target[0];
        }
        StringBuilder[] sbrArr = new StringBuilder[fieldFunArr.length];
        for (int i = 0; i < sbrArr.length; i++) {
            StringBuilder sbr = new StringBuilder();
            sbrArr[i] = sbr;
            sbr.append(target[i]).append(" = (case");
        }
        for (Map.Entry<Object, T> entry : map.entrySet()) {
            Object key = entry.getKey();
            T obj = entry.getValue();
            if (key == null || obj == null){
                continue;
            }
            boolean isArrObj = key instanceof Object[];
            for (int i = 0; i < fieldFunArr.length; i++) {
                Function<T, String> func = fieldFunArr[i];
                String value = func.apply(obj);
                StringBuilder sbr = sbrArr[i];
                sbr.append(" when ");
                for (int j = 0; j < sources.length; j++) {
                    if (!isArrObj && j > 0){
                        continue;
                    }
                    if (isArrObj && ((Object[])key).length  <= j){
                        continue;
                    }
                    String source = sources[j];
                    sbr.append(source);
                    Object setV = isArrObj?((Object[])key)[j]:key;
                    if (setV instanceof Object[]){
                        sbr.append(" in ('");
                        sbr.append(StringUtils.join((Object[]) setV,"','"));
                        sbr.append("')");
                    }else {
                        sbr.append(" = '").append(key).append("'");
                    }
                    sbr.append(" and ");
                }
                sbr.setLength(sbr.length() - 5);
                sbr.append(" then ");
                if (StringUtils.isBlank(value)){
                    sbr.append(target[i]);
                }else {
                    sbr.append("'").append(value).append("'");
                }
            }
        }
        StringBuilder ret = new StringBuilder();
        for (int i = 0; i < sbrArr.length; i++) {
            StringBuilder sbr = sbrArr[i];
            sbr.append(" else ").append(target[i]).append(" end)");
            ret.append(sbr).append(", ");
        }
        ret.setLength(ret.length()-2);
        return ret.toString();
    }
}
