package cloud.demand.app.modules.mrpv2.model;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map;

/** 缓存 */
@Data
public class MrpV2Cache {
    /** 是否开启缓存 */
    private Boolean enable = false;

    /** 缓存的维度,key-维度英文；value-维度范围，为空默认全部；备注：维度数量不能超过2个，超过两个则缓存失效
     * {@link cloud.demand.app.modules.mrpv2.enums.CacheDimEnum}*/
    private Map<String,List<String>> cacheDim;

    /** 默认为空，用于强制刷新缓存（这个值变了缓存就会变） */
    private String timeStamp;

    /** 缓存是否可用 */
    public boolean available(){
        // 不开启
        if (BooleanUtils.isNotTrue(enable)){
            return false;
        }
        // 维度为空或者维度超过2
        if (ListUtils.isEmpty(cacheDim) || cacheDim.size() > 2){
            return false;
        }

        return true;
    }

    // 例子 1：想查某个行业(同时缓存其他行业，下次查提升速度)，
    // "cache" : {"enable" = true, "cacheDim" = {"industryOrProduct":[]}}
    // "industryDepts": ["xxx"]

    // 例子 2：想查某个实例类型+地域（同时缓存其他地域+实例类型）
    // "cache" : {"enable" = true, "cacheDim" = {"regionName":[],"ginsFamily":[]}}
    // "regionNames": ["xxx"], "instanceTypes": ["xxxx"]
}
