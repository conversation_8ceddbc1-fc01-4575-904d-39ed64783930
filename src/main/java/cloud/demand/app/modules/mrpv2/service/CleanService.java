package cloud.demand.app.modules.mrpv2.service;

import cloud.demand.app.modules.mrpv2.model.clean.ICleanLongTailBurr;
import cloud.demand.app.modules.mrpv2.model.clean.ICleanNewCustomerType;
import cloud.demand.app.modules.mrpv2.model.clean.ICleanProjectType;

/** 清洗 */
public interface CleanService {
    public void clean(Object obj);

    public void cleanNewCustomerType(ICleanNewCustomerType obj);

    public void cleanProjectType(ICleanProjectType obj);

    public void cleanLongTailBurr(ICleanLongTailBurr obj);
}
