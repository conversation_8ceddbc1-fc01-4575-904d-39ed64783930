package cloud.demand.app.modules.industry_resource_month_report.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

/** gpu预扣 */
@Data
@NoArgsConstructor
public class GpuWithholdingModel {
    @ExcelProperty(value = "行业部门",index = 0)
    private String industryDept;
    @ExcelProperty(value = "通用客户简称",index = 1)
    private String unCustomerShortName;
    @ExcelProperty(value = "卡型",index = 2)
    private String gpuCardType;
    
    @ExcelProperty(value = "当前月预扣卡",index = 3)
    private Integer curGpuNumber;
    
    @ExcelProperty(value = "上个月预扣卡",index = 4)
    private Integer preGpuNumber;
    
    @ExcelProperty(value = "本期净增预扣卡",index = 5)
    private Integer diffGpuNumber;

    public GpuWithholdingModel(String industryDept, String unCustomerShortName, String gpuCardType,
            Integer curGpuNumber,
            Integer preGpuNumber, Integer diffGpuNumber) {
        this.industryDept = industryDept;
        this.unCustomerShortName = unCustomerShortName;
        this.gpuCardType = gpuCardType;
        this.curGpuNumber = ObjectUtils.defaultIfNull(curGpuNumber, 0);
        this.preGpuNumber = ObjectUtils.defaultIfNull(preGpuNumber, 0);
        this.diffGpuNumber = ObjectUtils.defaultIfNull(diffGpuNumber, 0);
    }
}
