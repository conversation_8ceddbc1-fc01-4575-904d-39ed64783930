package cloud.demand.app.modules.industry_cockpit.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_cockpit.constant.IndustryCockpitConstant;
import cloud.demand.app.modules.industry_cockpit.entity.DemandTrendVO;
import cloud.demand.app.modules.industry_cockpit.model.IndustryCockpoitRequest;
import cloud.demand.app.modules.industry_cockpit.service.AuthService;
import cloud.demand.app.modules.industry_cockpit.service.DemandTrendsTableService;
import cloud.demand.app.modules.soe.enums.DateTypeEnum;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.TemporalAdjusters;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/5/11
 */
@Service
public class DemandTrendsLastYearTableServiceImpl implements DemandTrendsTableService {
    @Resource
    private DBHelper ckcldDBHelper;
    @Autowired
    @Qualifier("authServiceTxyScaleImpl")
    private AuthService authServiceTxyScaleImpl;

    @Override
    public List<DemandTrendVO> getDemandTrendsTable(IndustryCockpoitRequest request, String demandType) {
        WhereSQL historyChangeWhere = authServiceTxyScaleImpl.getAuthWhereSql();

        if (null == historyChangeWhere) {
            return Collections.emptyList();
        }

        String historyChangeFixSql = "and industry_dept != '(空值)' and instance_type not like '%RM%' and instance_type not like '%RS%' and biz_range_type = '外部业务' and app_role in ('正常售卖', 'CDH', '预扣包', 'GOCP', 'CDZ') "; // and customer_tab_type != '中长尾客户'

        if ("CVM".equals(request.getProduct())) {
            historyChangeWhere.and("product in (?) ", "CVM");
        }

        int year = StringUtils.isEmpty(request.getDemandYear()) ? LocalDate.now().getYear() - 1 : Integer.parseInt(request.getDemandYear()) - 1;

        LocalDate start = YearMonth.of(year, 1).atDay(1);
        LocalDate end = YearMonth.of(year, 12).atDay(1).with(TemporalAdjusters.lastDayOfMonth());

        // 月末时间集合,当月取昨天
        List<String> range = SoeCommonUtils.getRange(DateTypeEnum.YearMonth.getName(), start, end, LocalDate.now().plusDays(-1));

        historyChangeWhere.and("stat_time in (?) ", range);

        if (!CollectionUtils.isEmpty(request.getIndustryDept())) {
            historyChangeWhere.and("industry_dept in (?)", request.getIndustryDept());
        }
        if (!CollectionUtils.isEmpty(request.getWarZone())) {
            historyChangeWhere.and("crp_war_zone in (?)", request.getWarZone());
        }
        if (!CollectionUtils.isEmpty(request.getCustomerShortNames())) {
            historyChangeWhere.and("un_customer_short_name in (?)", request.getCustomerShortNames());
        }
        if (!CollectionUtils.isEmpty(request.getInstanceType())) {
            historyChangeWhere.and("instance_type in (?)", request.getInstanceType());
        }
        if (!CollectionUtils.isEmpty(request.getCustomhouseTitle())) {
            historyChangeWhere.and("customhouse_title in (?)", request.getCustomhouseTitle());
        }
        if (!CollectionUtils.isEmpty(request.getRegionName())) {
            historyChangeWhere.and("region_name in (?)", request.getRegionName());
        }
        if (!CollectionUtils.isEmpty(request.getZoneName())) {
            historyChangeWhere.and("zone_name in (?)", request.getZoneName());
        }
        if (Objects.nonNull(request.getCustomerRange())) {
            historyChangeWhere.and(" is_inner = ?  ", request.getCustomerRange());
        }
        String currentYearSql = ORMUtils.getSql("/sql/industry_cockpit/9_1_demand_trend_lastyear_table.sql");
        currentYearSql = currentYearSql.replace("${HISTORY_FILTER}", historyChangeWhere.getSQL());
        currentYearSql = currentYearSql.replace("${queryRange}", request.getQueryRange());
        // 根据前端传的维度去做group by
        if (request.getDims().contains(IndustryCockpitConstant.INDUSTRY_DEPT)) {
            currentYearSql = currentYearSql.replaceAll("\\$\\{industry_dept_field}", "industry_dept, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{industry_dept_group}", ",industry_dept ");
        } else {
            currentYearSql = currentYearSql.replaceAll("\\$\\{industry_dept_field}", "'' as industry_dept, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{industry_dept_group}", " ");
        }
        if (request.getDims().contains(IndustryCockpitConstant.WAR_ZONE)) {
            currentYearSql = currentYearSql.replaceAll("\\$\\{war_zone_field}", "war_zone, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{war_zone_group}", ",war_zone ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{has_war_zone}", "1");
        } else {
            currentYearSql = currentYearSql.replaceAll("\\$\\{war_zone_field}", "'' as war_zone, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{war_zone_group}", " ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{has_war_zone}", "0");
        }
        if (request.getDims().contains(IndustryCockpitConstant.CUSTOMER_SHORT_NAME)) {
            currentYearSql = currentYearSql.replaceAll("\\$\\{customer_short_name_field}", "customer_short_name, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{customer_short_name_group}", ",customer_short_name ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{has_customer_short_name}", "1");
        } else {
            currentYearSql = currentYearSql.replaceAll("\\$\\{customer_short_name_field}", "'' as customer_short_name, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{customer_short_name_group}", " ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{has_customer_short_name}", "0");
        }
        if (request.getDims().contains(IndustryCockpitConstant.INSTANCE_TYPE)) {
            currentYearSql = currentYearSql.replaceAll("\\$\\{instance_type_field}", "instance_type, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{instance_type_group}", ",instance_type ");
        } else {
            currentYearSql = currentYearSql.replaceAll("\\$\\{instance_type_field}", "'' as instance_type, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{instance_type_group}", " ");
        }
        if (request.getDims().contains(IndustryCockpitConstant.REGION_NAME)) {
            currentYearSql = currentYearSql.replaceAll("\\$\\{region_name_field}", " region_name, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{region_name_group}", " ,region_name ");
        } else {
            currentYearSql = currentYearSql.replaceAll("\\$\\{region_name_field}", " '' as region_name, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{region_name_group}", " ");
        }
        if (request.getDims().contains(IndustryCockpitConstant.ZONE_NAME)) {
            currentYearSql = currentYearSql.replaceAll("\\$\\{zone_name_field}", "zone_name, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{zone_name_group}", ", zone_name");
        } else {
            currentYearSql = currentYearSql.replaceAll("\\$\\{zone_name_field}", " '' as zone_name, ");
            currentYearSql = currentYearSql.replaceAll("\\$\\{zone_name_group}", " ");
        }

        currentYearSql = currentYearSql.replace("${HISTORY_CHANGE_FIX_WHERE}", historyChangeFixSql);

        currentYearSql = currentYearSql.replaceAll("\\$\\{demand_type\\}", demandType);

        // 参数合并
        Object[] historyParams = historyChangeWhere.getParams();

        // 获取今年的预测增量
        List<DemandTrendVO> demandTrendVO = ckcldDBHelper.getRaw(DemandTrendVO.class, currentYearSql, historyParams);

        return demandTrendVO;
    }
}
