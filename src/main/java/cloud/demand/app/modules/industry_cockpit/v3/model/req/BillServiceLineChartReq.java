package cloud.demand.app.modules.industry_cockpit.v3.model.req;

import cloud.demand.app.modules.industry_cockpit.auth.AuthCheckParam;
import cloud.demand.app.modules.industry_cockpit.v3.parse.ProductBizTypeParse;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/29 19:13
 */
@Data
public class BillServiceLineChartReq extends LineChartReq{
    @NotNull(message = "产品不能为空")
    @SopReportWhere(parsers = {ProductBizTypeParse.class})
    @AuthCheckParam(authField = "product")
    private String product;

    @SopReportWhere(sql = " origin_industry_dept in (?) ")
    @AuthCheckParam(authField = "industry")
    private List<String> industryDept; // 行业部门 支持多选
}
