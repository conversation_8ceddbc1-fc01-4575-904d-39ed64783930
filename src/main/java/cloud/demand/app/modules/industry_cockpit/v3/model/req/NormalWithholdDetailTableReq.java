package cloud.demand.app.modules.industry_cockpit.v3.model.req;

import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/26 16:45
 */
@Data
public class NormalWithholdDetailTableReq extends NormalWithholdReq implements IInstanceFamilyDimReq {


    @NotNull(message = "statTime不能为空")
    @SopReportWhere
    private LocalDate statTime;

    private List<String> dims = ListUtils.newArrayList();// 维度:industryDept、warZone、customerShortName、instanceType、gpuCardType、regionName、
    // zoneName、appId、uin、instanceModel、reserveMode、withholdDuration

    private Integer topN;

    public static NormalWithholdDetailTableReq transform(LogicalTagsWithholdReq req) {
        NormalWithholdDetailTableReq ret = new NormalWithholdDetailTableReq();
        BeanUtils.copyProperties(req, ret);
        return ret;
    }
}
