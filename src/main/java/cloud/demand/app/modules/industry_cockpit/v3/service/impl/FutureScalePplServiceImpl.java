package cloud.demand.app.modules.industry_cockpit.v3.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.industry_cockpit.constant.IndustryCockpitConstant;
import cloud.demand.app.modules.industry_cockpit.v3.constant.IndustryCockpitV3Constant;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.FutureScaleTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.FutureScaleMonthlyVO;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.VersionInfoVO;
import cloud.demand.app.modules.industry_cockpit.v3.service.FieldFillService;
import cloud.demand.app.modules.industry_cockpit.v3.service.FutureScaleService;
import cloud.demand.app.modules.industry_cockpit.v3.service.IndustryCockpitV3DictService;
import cloud.demand.app.modules.industry_cockpit.v3.service.IndustryCockpitV3DictService.SimpleCustomerInfo;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.enums.DemandTypeEnum;
import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermGroupItemWithDeptDTO;
import cloud.demand.app.modules.p2p.longterm.controller.req.LongtermDemandStatsReq;
import cloud.demand.app.modules.p2p.longterm.controller.resp.LongtermDemandStatsResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.LongtermDemandStatsResp.Item;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupItemTimeUnitEnum;
import cloud.demand.app.modules.p2p.longterm.service.LongtermStatsService;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplItemVersionCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwsCrpPplItemVersionNewestCfDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/10/12 14:48
 */
@Service
@Slf4j
public class FutureScalePplServiceImpl implements FutureScaleService {

    @Resource
    private IndustryCockpitV3DictService industryCockpitV3DictService;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private FieldFillService fieldFillService;

    /**
     * 中长期预测
     */
    @Resource
    private LongtermStatsService longtermStatsService;

    @Override
    public List<FutureScaleMonthlyVO> getFutureScaleMonthly(FutureScaleTableReq req) {

        List<String> appIdToUinList = industryCockpitV3DictService.getUinByAppId(req.getAppId());
        List<String> notAppIdToUinList = industryCockpitV3DictService.getUinByAppId(req.getNotAppId());
        VersionInfoVO versionInfo = req.getVersionInfo();

        String predictionFixSql = "";
        if (IndustryCockpitConstant.INTERVENED.equals(req.getIntervene())) {
            // 已干预
            predictionFixSql = "and is_comd != 1 and ((industry_dept != '中长尾' AND source NOT IN ('FORECAST','APPLY_AUTO_FILL_LONGTAIL')) or (industry_dept = '中长尾' and source IN ('FORECAST')))";
        } else {
            // 未干预
            predictionFixSql = "and source in ('IMPORT','APPLY_AUTO_FILL','FORECAST') and demand_type in ('NEW', 'ELASTIC', 'RETURN')";
        }

        // 查询PPL版本信息
        SopWhereBuilder pplVersionWhereBuilder = new SopWhereBuilder(req, DwdCrpPplItemVersionCfDO.class);
        ORMUtils.WhereContent pplVersionWhere = pplVersionWhereBuilder.where();
        pplVersionWhere.andInIfValueNotEmpty(" customer_uin ", appIdToUinList);
        if (ListUtils.isNotEmpty(notAppIdToUinList)) {
            pplVersionWhere.andNotIn(" customer_uin ", notAppIdToUinList);
        }


        // 查询PPL最新版本
        SopWhereBuilder pplNewestWhereBuilder = new SopWhereBuilder(req, DwsCrpPplItemVersionNewestCfDO.class);
        ORMUtils.WhereContent pplNewestWhere = pplNewestWhereBuilder.where();
        pplNewestWhere.andInIfValueNotEmpty(" customer_uin ", appIdToUinList);
        if (ListUtils.isNotEmpty(notAppIdToUinList)) {
            pplNewestWhere.andNotIn(" customer_uin ", notAppIdToUinList);
        }

        if (Objects.nonNull(versionInfo)) {
            // PPL指定版本
            pplVersionWhere.addAnd(" version_code = ?  ", versionInfo.getVersionCode());
            pplVersionWhere.addAnd(" toYYYYMM(begin_buy_date) >= ? ", versionInfo.getStartMonth());
            pplVersionWhere.addAnd(" toYYYYMM(begin_buy_date) <= ? ", versionInfo.getEndMonth());

            // 最新版排除指定版本的数据
            ORMUtils.WhereContent orWhereContent = new ORMUtils.WhereContent();
            orWhereContent.addOr(" toYYYYMM(begin_buy_date) < ? ", versionInfo.getStartMonth());
            orWhereContent.addOr(" toYYYYMM(begin_buy_date) > ? ", versionInfo.getEndMonth());
            pplNewestWhere.addAnd(orWhereContent);
        }
        String sql = ORMUtils.getSql("/sql/industry_cockpit/v3/12_future_scale_ppl.sql");
        sql = SimpleSqlBuilder.doReplace(sql, "unit", req.getUnit());
        sql = SimpleSqlBuilder.doReplace(sql, "demandType", req.getDemandType());

        sql = SimpleSqlBuilder.doReplace(sql, "pplVersionWhere", pplVersionWhere.getSql());
        sql = SimpleSqlBuilder.doReplace(sql, "pplNewestWhere", pplNewestWhere.getSql());

        sql = SimpleSqlBuilder.doReplace(sql, "predictionFixSql", predictionFixSql);

        List<String> fieldNames = Arrays.stream(FutureScaleMonthlyVO.class.getDeclaredFields()).map(item -> item.getName()).collect(Collectors.toList());

        sql = fieldFillService.fillInstanceFamilyDim(req, sql, fieldNames); // 补充 instanceFamily 字段 (instance_type 映射关系关联 instance_family

        sql = SimpleSqlBuilder.buildDims(sql, new HashSet<>(fieldNames), req.getDims());

        Object[] allParams = Stream.concat(Arrays.stream(pplVersionWhere.getParams()), Arrays.stream(pplNewestWhere.getParams())).toArray();
        return ckcldStdCrpDBHelper.getRaw(FutureScaleMonthlyVO.class, sql, allParams);
    }

    /**
     * 参考页面：<a href="https://crp.woa.com/long-term/analysis">中长期需求波动分析</a>
     */
    @Override
    public List<FutureScaleMonthlyVO> getLongTermFutureScaleMonthly(FutureScaleTableReq req) {
        String longTermVersionCode = req.getLongTermVersionCode();
        if (StringUtils.isBlank(longTermVersionCode)) {
            throw new BizException("中长期预测版本号不能为空");
        }
        List<String> isMigrate = req.getIsMigrate();

        LongtermDemandStatsReq longReq = new LongtermDemandStatsReq();
        longReq.setCurrentVersionCode(longTermVersionCode);
        longReq.setIsBeforeIntervene(Objects.equals(req.getIntervene(), "干预前"));
        LongtermDemandStatsResp demandStatsDetail = longtermStatsService.getDemandStatsDetail(longReq); // 接口内部不做过滤

        Item current = demandStatsDetail.getCurrent();
        List<LongtermGroupItemWithDeptDTO> items = current == null ? ListUtils.newList() : current.getItems();
        List<String> notCustomerShortName = new ArrayList<>();

        // 过滤
        if (ListUtils.isNotEmpty(items)) {
            List<Predicate<LongtermGroupItemWithDeptDTO>> filter = new ArrayList<>(); // 过滤
            // 基础信息
            List<String> product = req.getProduct();
            // 需求管理颗粒度
            List<String> industryDept = req.getIndustryDept(); // 行业部门
            List<String> customerShortName = ObjectUtils.defaultIfNull(req.getCustomerShortName(), new ArrayList<>()); // 客户简称(通用)
            {
                List<String> warZone = req.getWarZone();
                List<String> uin = req.getUin();
                List<String> appId = req.getAppId();
                List<String> notUin = req.getNotUin();
                List<String> notAppId = req.getNotAppId();
                List<String> unCustomerShortNameWithUinAnaAppId = industryCockpitV3DictService.getUnCustomerShortNameWithUinAnaAppId(warZone, uin, appId, notUin, notAppId);
                if (ListUtils.isNotEmpty(unCustomerShortNameWithUinAnaAppId)) {
                    customerShortName = ListUtils.intersection(customerShortName, unCustomerShortNameWithUinAnaAppId);
                }
                List<String> combinedCustomerShortName = req.getCombinedCustomerShortName();
                if (ListUtils.isNotEmpty(combinedCustomerShortName)) {
                    boolean hasOther = combinedCustomerShortName.stream().anyMatch(item -> com.google.common.base.Objects.equal(item, IndustryCockpitV3Constant.OTHER_CUSTOMER));
                    if (hasOther) {
                        // 包含其他则用排除法
                        IndustryCockpitV3DictService bean = SpringUtil.getBean(IndustryCockpitV3DictService.class);
                        List<String> showCustomerShortName = bean.getShowCustomerShortName();
                        Set<String> set = new HashSet<>(combinedCustomerShortName);
                        showCustomerShortName.removeIf(set::contains);
                        notCustomerShortName.addAll(showCustomerShortName);
                    } else if (ListUtils.isNotEmpty(customerShortName)) {
                        customerShortName = ListUtils.intersection(customerShortName, combinedCustomerShortName);
                    } else {
                        customerShortName = combinedCustomerShortName;
                    }
                }
            }
            // 资源颗粒度
            List<String> customhouseTitle = req.getCustomhouseTitle();
            List<String> instanceType = req.getInstanceType();
            List<String> instanceFamily = req.getInstanceFamily();
            List<String> gpuCardType = req.getGpuCardType();
            List<String> regionName = req.getRegionName();
            List<String> zoneName = req.getZoneName();
            // 需求类型
            String demandType = req.getDemandType();
            List<String> demandTypeList = new ArrayList<>();
            {
                if (Objects.equals(demandType, "新增&弹性")) {
                    demandTypeList.add(DemandTypeEnum.NEW.name());
                    demandTypeList.add(DemandTypeEnum.ELASTIC.name());
                } else if (Objects.equals(demandType, "退回")) {
                    demandTypeList.add(DemandTypeEnum.RETURN.name());
                } else if (Objects.equals(demandType, "新增")) {
                    demandTypeList.add(DemandTypeEnum.NEW.name());
                } else if (Objects.equals(demandType, "弹性")) {
                    demandTypeList.add(DemandTypeEnum.ELASTIC.name());
                }
            }

            if (BooleanUtils.isTrue(req.getIsPaas())) {
                addFilter(filter, ListUtils.newList(Ppl13weekProductTypeEnum.PAAS.getName()), LongtermGroupItemWithDeptDTO::getProduct);
                addFilter(filter, product, LongtermGroupItemWithDeptDTO::getPaasProduct);
            } else {
                addFilter(filter, product, LongtermGroupItemWithDeptDTO::getProduct);
            }
            addFilter(filter, isMigrate, LongtermGroupItemWithDeptDTO::getIsMigrate);
            addFilter(filter, industryDept, LongtermGroupItemWithDeptDTO::getIndustryDept);
            addFilter(filter, customerShortName, LongtermGroupItemWithDeptDTO::getCustomerShortName);
            addFilter(filter, notCustomerShortName, LongtermGroupItemWithDeptDTO::getCustomerShortName, false);
            addFilter(filter, customhouseTitle, LongtermGroupItemWithDeptDTO::getCustomhouseTitle);
            addFilter(filter, instanceType, LongtermGroupItemWithDeptDTO::getInstanceType);
            addFilter(filter, instanceFamily, LongtermGroupItemWithDeptDTO::getInstanceFamily);
            addFilter(filter, gpuCardType, LongtermGroupItemWithDeptDTO::getGpuType);
            addFilter(filter, regionName, LongtermGroupItemWithDeptDTO::getRegionName);
            addFilter(filter, zoneName, LongtermGroupItemWithDeptDTO::getZoneName);
            addFilter(filter, demandTypeList, LongtermGroupItemWithDeptDTO::getDemandType);

            if (ListUtils.isNotEmpty(filter)) {
                items = items.stream().filter(item -> {
                    for (Predicate<LongtermGroupItemWithDeptDTO> f : filter) {
                        if (!f.test(item)) {
                            return false;
                        }
                    }
                    return true;
                }).collect(Collectors.toList());
            }
        }

        Map<String, SimpleCustomerInfo> unCustomerShortNameMap = industryCockpitV3DictService.getUnCustomerShortNameMap();

        List<FutureScaleMonthlyVO> ret = new ArrayList<>();
        List<String> dims = req.getDims(); // 维度
        Map<String, Tuple2<Function<LongtermGroupItemWithDeptDTO, String>, BiConsumer<FutureScaleMonthlyVO, String>>> transformMap = new HashMap<>();
        List<Tuple2<Function<LongtermGroupItemWithDeptDTO, String>, BiConsumer<FutureScaleMonthlyVO, String>>> transformList = new ArrayList<>();
        // 年月默认维度
        transformMap.put("yearMonth", new Tuple2<>(this::getYearMonthFromLongTerm, FutureScaleMonthlyVO::setYearMonth));
        transformList.add(transformMap.get("yearMonth"));
        if (ListUtils.isNotEmpty(dims)) {
            transformMap.put("industryDept", new Tuple2<>(LongtermGroupItemWithDeptDTO::getIndustryDept, FutureScaleMonthlyVO::setIndustryDept));
            transformMap.put("warZone", new Tuple2<>(LongtermGroupItemWithDeptDTO::getCustomerShortName, (item, v) -> this.setWarZoneWithCustomerShortName(item, v, unCustomerShortNameMap)));
            transformMap.put("customerShortName", new Tuple2<>(LongtermGroupItemWithDeptDTO::getCustomerShortName, FutureScaleMonthlyVO::setCustomerShortName));
            transformMap.put("instanceType", new Tuple2<>(LongtermGroupItemWithDeptDTO::getInstanceType, FutureScaleMonthlyVO::setInstanceType));
            transformMap.put("instanceFamily", new Tuple2<>(LongtermGroupItemWithDeptDTO::getInstanceFamily, FutureScaleMonthlyVO::setInstanceFamily));
            transformMap.put("gpuCardType", new Tuple2<>(LongtermGroupItemWithDeptDTO::getGpuType, FutureScaleMonthlyVO::setGpuCardType));
            transformMap.put("customhouseTitle", new Tuple2<>(LongtermGroupItemWithDeptDTO::getCustomhouseTitle, FutureScaleMonthlyVO::setCustomhouseTitle));
            transformMap.put("regionName", new Tuple2<>(LongtermGroupItemWithDeptDTO::getRegionName, FutureScaleMonthlyVO::setRegionName));
            transformMap.put("zoneName", new Tuple2<>(LongtermGroupItemWithDeptDTO::getZoneName, FutureScaleMonthlyVO::setZoneName));
            for (String dim : dims) {
                transformList.add(transformMap.get(dim));
            }
        }

        boolean isGpu = Objects.equals(req.getUnit(), "卡数");

        for (LongtermGroupItemWithDeptDTO item : items) {
            FutureScaleMonthlyVO vo = new FutureScaleMonthlyVO();
            transformList.forEach(tuple2 -> tuple2._2().accept(vo, tuple2._1().apply(item)));
            BigDecimal gpuOrCoreNum = isGpu ? item.getGpuNum() : item.getCoreNum();
            vo.setAmount(gpuOrCoreNum);
            ret.add(vo);
        }
        return ret;
    }

    /**
     * 获取中长期年月
     *
     * @param dto
     * @return
     */
    private String getYearMonthFromLongTerm(LongtermGroupItemWithDeptDTO dto) {
        Integer demandMonth = dto.getDemandMonth();
        Integer demandQuarter = dto.getDemandQuarter();
        Integer demandYear = dto.getDemandYear();
        String timeUnit = dto.getTimeUnit();
        LongtermVersionGroupItemTimeUnitEnum byCode = LongtermVersionGroupItemTimeUnitEnum.getByCode(timeUnit);
        if (byCode == null) {
            throw new IllegalArgumentException("timeUnit:" + timeUnit + " not support");
        }
        switch (byCode) {
            case MONTH:
                return SopDateUtils.getYearMonth(demandYear, demandMonth);
            case QUARTER:
                return SopDateUtils.getYearMonth(demandYear, (demandQuarter - 1) * 3 + 1);
            case YEAR:
                return SopDateUtils.getYearMonth(demandYear, 1);
            default:
                throw new IllegalArgumentException("timeUnit:" + timeUnit + " not support");
        }
    }

    private void setWarZoneWithCustomerShortName(FutureScaleMonthlyVO vo, String customerShortName, Map<String, SimpleCustomerInfo> unCustomerShortNameMap) {

        SimpleCustomerInfo simpleCustomerInfo = unCustomerShortNameMap.get(customerShortName);
        if (simpleCustomerInfo == null) {
            vo.setWarZone(Constant.EMPTY_VALUE);
        } else {
            vo.setWarZone(simpleCustomerInfo.getWarZone());
        }
    }

    /**
     * 获取过滤器
     *
     * @param filterDict 过滤的数据字典
     * @param getter     获取字段的函数
     * @return 过滤器
     */
    private void addFilter(List<Predicate<LongtermGroupItemWithDeptDTO>> filter, List<String> filterDict, Function<LongtermGroupItemWithDeptDTO, String> getter) {
        addFilter(filter, filterDict, getter, true);
    }

    private void addFilter(List<Predicate<LongtermGroupItemWithDeptDTO>> filter, List<String> filterDict, Function<LongtermGroupItemWithDeptDTO, String> getter, boolean contains) {
        if (ListUtils.isNotEmpty(filterDict)) {
            Set<String> set = new HashSet<>(filterDict);
            Predicate<LongtermGroupItemWithDeptDTO> f = item -> contains == set.contains(getter.apply(item));
            filter.add(f);
        }
    }
}
