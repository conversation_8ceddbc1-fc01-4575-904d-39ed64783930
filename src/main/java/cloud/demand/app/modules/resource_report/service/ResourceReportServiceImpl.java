package cloud.demand.app.modules.resource_report.service;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.entity.ck_cloud_demand.AdsTcresAnnualDemandReportDO;
import cloud.demand.app.entity.ck_cloud_demand.AdsTcresAnnualDemandReportForQueryDO;
import cloud.demand.app.entity.ck_cloud_demand.AdsTcresAnnualDemandReportForQueryVO;
import cloud.demand.app.entity.ck_cloud_demand.ResourceReportALForJson;
import cloud.demand.app.entity.ck_cloud_demand.ResourceReportBHCDEForJson;
import cloud.demand.app.entity.ck_cloud_demand.ResourceReportFGIForJson;
import cloud.demand.app.model.demand.DemandReportAnalyseNoteDO;
import cloud.demand.app.modules.resource_report.req.QueryIndexDataReq;
import cloud.demand.app.modules.resource_report.req.QueryNoteReq;
import cloud.demand.app.modules.resource_report.req.QueryResourceReportItemReq;
import cloud.demand.app.modules.resource_report.rsp.DateVersionVo;
import cloud.demand.app.modules.resource_report.rsp.QueryDateDetailsRsp;
import cloud.demand.app.modules.resource_report.rsp.QueryDateDetailsRsp.DateItem;
import cloud.demand.app.modules.resource_report.rsp.QueryForecastDiffRsp;
import cloud.demand.app.modules.resource_report.rsp.QueryForecastDiffRsp.DiffItem;
import cloud.demand.app.modules.resource_report.rsp.QueryNoteRsp;
import cloud.demand.app.modules.resource_report.rsp.QueryOrderDiffRsp;
import cloud.demand.app.modules.resource_report.rsp.QueryResourceReportItemRsp;
import cloud.demand.app.modules.resource_report.rsp.QueryResourceReportItemRsp.Data;
import cloud.demand.app.modules.resource_report.rsp.QueryResourceReportItemRsp.Data.IndexItem;
import cloud.demand.app.modules.resource_view.enums.BigReportIdxEnum;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.utils.DOInfoReader;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class ResourceReportServiceImpl implements
        ResourceReportService {


    @Resource
    DBHelper ckcldDBHelper;
    @Resource
    DBHelper demandDBHelper;
    @Resource
    ResourceReportService resourceReportService;

    @Resource
    private DBHelper rrpDBHelper;

    /**
     * 查询汇总数据
     */
    @Override
    public QueryResourceReportItemRsp resourceReportItem(QueryResourceReportItemReq req) {

        List<String> reqColumnValues = req.getDim() == null ? Lang.list() : req.getDim().stream()
                .map((o) -> ORMUtils.getColumnByFieldName(AdsTcresAnnualDemandReportDO.class, o))
                .collect(Collectors.toList());
        log.info("step1 ");
        WhereContent whereContent = getResoureReportWhere(req);
        {
            whereContent.groupBy(ORMUtils.getColumnByMethod(AdsTcresAnnualDemandReportDO::getVersion));
            whereContent.groupBy(ORMUtils.getColumnByMethod(AdsTcresAnnualDemandReportDO::getPIndex));
            whereContent.groupBy(reqColumnValues);
        }

        List<AdsTcresAnnualDemandReportForQueryVO> allData = ORMUtils.db(ckcldDBHelper)
                .getAll(AdsTcresAnnualDemandReportForQueryVO.class, whereContent);
        log.info("step2 ");
        return transFrom(allData, req);
    }

    private WhereContent getResoureReportWhere(QueryResourceReportItemReq req) {

        WhereContent whereContent = new WhereContent();
        {
            WhereContent versionWhere = new WhereContent();
            versionWhere.andEqualIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getVersion,
                    req.getStartVersion());
            versionWhere.orEqualIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getVersion, req.getEndVersion());
            whereContent.addAnd(versionWhere);
        }

        whereContent.addAnd("p_ym between ?  and  ? ", req.getStartMonth(), req.getEndMonth());

        whereContent.andInIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getDimBigClass, req.getDimBigClass());
        whereContent.andInIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getDimProductClass, req.getDimProductClass());
        whereContent.andInIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getDimDeviceType, req.getDimDeviceType());
        whereContent.andInIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getDimInstanceType, req.getDimInstanceType());
        whereContent.andInIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getDimCpuType, req.getDimCpuType());
        whereContent.andInIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getDimNetType, req.getDimNetType());
        whereContent.andInIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getDimRegionClass, req.getDimRegionClass());
        whereContent.andInIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getDimRegion, req.getDimRegion());
        whereContent.andInIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getDimIndustry, req.getDimIndustry());
        whereContent.andInIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getDimCustomer, req.getDimCustomer());

        whereContent.andInIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getDimPlanProduct, req.getDimPlanProduct());
        whereContent.andInIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getDimReason1, req.getDimReason1());
        whereContent.andInIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getDimReason2, req.getDimReason2());
        whereContent.andInIfValueNotEmpty(AdsTcresAnnualDemandReportDO::getDimSupplyWay, req.getDimSupplyWay());

        return whereContent;
    }


    @Override
    public ImmutableMap<String, List<String>> queryParams(QueryResourceReportItemReq req) {
        String paramType = req.getParamType();
        if (Strings.isBlank(paramType)) {
            return ImmutableMap.of("data", Lang.list());
        }
        //内部方法调用，注入自身bean.构建代理对象
        List<String> raw = resourceReportService.queryByParamType(paramType);
        raw = raw.stream().sorted(Comparator.comparing(String::toString, Comparator.nullsLast((c1, c2) -> {
            if (Strings.equals(c1, "")) {
                return 1;
            }
            if (Strings.equals(c2, "")) {
                return -1;
            }
            return 0;
        }))).collect(Collectors.toList());
        return ImmutableMap.of("data", raw);
    }

    @HiSpeedCache(expireSecond = 500, keyScript = "args[0]")
    @Override
    public List<String> queryByParamType(String paramType) {
        String columnByFildName = ORMUtils.getColumnByFieldName(AdsTcresAnnualDemandReportDO.class, paramType);

        //取最近半个月数据的集合
        String ver = DateUtils.format(LocalDate.now().minusDays(15), "yyyyMMdd");
        // 这里防止sql 注入检测到我的sql有注入风险
        String sql =
                "select distinct $paramType from ads_tcres_demand_annual_excueted_report where version > '" + ver + "' "
                        + "and isNotNull($paramType) "
                        + "order by $paramType";
        sql = sql.replace("$paramType", columnByFildName);
        return ckcldDBHelper.getRaw(String.class, sql);
    }


    @Override
    public QueryDateDetailsRsp queryIndexDetails(QueryIndexDataReq req) {

        List<String> reqColumnValues = req.getDim() == null ? Lang.list() : req.getDim().stream()
                .map((o) -> ORMUtils.getColumnByFieldName(AdsTcresAnnualDemandReportDO.class, o))
                .collect(Collectors.toList());

        WhereContent whereContent = getResoureReportWhere(req);
        {
            whereContent.groupBy(ORMUtils.getColumnByMethod(AdsTcresAnnualDemandReportDO::getVersion));
            whereContent.groupBy(reqColumnValues);
            if (!Lang.isEmpty(req.getFilterIndexValue())) {
                for (String j : req.getFilterIndexValue()) {
                    whereContent.addAnd("p_index=?", j);
                }
            }
        }
        List<AdsTcresAnnualDemandReportForQueryVO> all = ORMUtils.db(ckcldDBHelper)
                .getAll(AdsTcresAnnualDemandReportForQueryVO.class, whereContent);

        Map<String, List<AdsTcresAnnualDemandReportForQueryVO>> groupByData = ListUtils.groupBy(all, (o) -> {
            List<Object> valueByName = getValueByName(AdsTcresAnnualDemandReportForQueryDO.class, req.getDim(), o);
            return Strings.join("@", valueByName);
        });
        List<QueryDateDetailsRsp.DateItem> ret = Lang.list();
        for (String key : groupByData.keySet()) {
            List<AdsTcresAnnualDemandReportForQueryVO> value = groupByData.get(key);
            QueryDateDetailsRsp.DateItem one = transFrom(value, req.getStartVersion(), req.getEndVersion());
            one.setDim(key);
            ret.add(one);
        }

        // 默认按照 变化量绝对值倒序排序
        ret = ret.stream().sorted(Comparator.comparing((o) -> {
                    BigDecimal num1 = o.getNum1() == null ? BigDecimal.ZERO : o.getNum1();
                    BigDecimal num2 = o.getNum2() == null ? BigDecimal.ZERO : o.getNum2();
                    return num2.subtract(num1).abs();
                },
                Comparator.nullsLast(Comparator.reverseOrder()))).collect(Collectors.toList());

        if (Lang.isNotEmpty(req.getDim()) && req.getDim().size() == 1) {
            if (Strings.equals(req.getDim().get(0), "date")) {
                ret = ret.stream().sorted(Comparator.comparing(DateItem::getDate,
                        Comparator.nullsLast(String::compareTo))).collect(Collectors.toList());
            }
            if (Strings.equals(req.getDim().get(0), "yearMonth")) {
                ret = ret.stream().sorted(Comparator.comparing(DateItem::getYearMonth,
                        Comparator.nullsLast(String::compareTo))).collect(Collectors.toList());
            }
        }

        BigDecimal num1 = ListUtils.sum(ret, DateItem::getNum1);
        BigDecimal num2 = ListUtils.sum(ret, DateItem::getNum2);
        BigDecimal core1 = ListUtils.sum(ret, DateItem::getCore1);
        BigDecimal core2 = ListUtils.sum(ret, DateItem::getCore2);
        return new QueryDateDetailsRsp(ret, num1, num2, core1, core2);
    }

    @Override
    @SneakyThrows
    public ImmutableMap<String, Object> insertNode(QueryNoteReq req) {

        List<String> dimName = req.getDimName();
        List<String> dimValue = req.getDimValue();

        DemandReportAnalyseNoteDO data2insert = transNoteFrom(req);
        if (dimName != null && dimValue != null) {
            if (dimName.size() != dimValue.size()) {
                throw Lang.makeThrow("维度数据不一样");
            }
            for (int j = 0; j < dimName.size(); j++) {
                Field declaredField = DemandReportAnalyseNoteDO.class.getDeclaredField(dimName.get(j));
                DOInfoReader.setValue(declaredField, data2insert, dimValue.get(j));
            }
        }
        demandDBHelper.insert(data2insert);
        return ImmutableMap.of("data", data2insert, "info", "success");
    }

    @Override
    public QueryNoteRsp queryNote(QueryNoteReq req) {
        WhereContent whereContent = new WhereContent();

        whereContent.andEqualIfValueNotNull(DemandReportAnalyseNoteDO::getStartVersion, req.getStartVersion());
        whereContent.andEqualIfValueNotNull(DemandReportAnalyseNoteDO::getEndVersion, req.getEndVersion());

        // 日期范围展示不管

        whereContent.andInIfValueNotEmpty(DemandReportAnalyseNoteDO::getPIndex, req.getFilterIndexValue());

        List<String> dimName = req.getDimName();
        List<String> dimValue = req.getDimValue();
        if (dimName != null && dimValue != null) {
            if (dimName.size() != dimValue.size()) {
                throw Lang.makeThrow("维度数据不一样");
            }
            for (int j = 0; j < dimName.size(); j++) {
                String columnByFieldName = ORMUtils.getColumnByFieldName(DemandReportAnalyseNoteDO.class,
                        dimName.get(j));
                whereContent.andEqual(columnByFieldName, dimValue.get(j));
            }
        }
        List<DemandReportAnalyseNoteDO> all = ORMUtils.db(demandDBHelper)
                .getAll(DemandReportAnalyseNoteDO.class, whereContent);
        return new QueryNoteRsp(all);
    }

    @Override
    public ImmutableMap<String, String> deleteNoteById(QueryNoteReq req) {
        DemandReportAnalyseNoteDO deleteId = new DemandReportAnalyseNoteDO();
        deleteId.setId(req.getId());
        deleteId.setDeleted(true);
        deleteId.setUpdater(req.getUserName());
        int i = demandDBHelper.update(deleteId);
        String info = i == 0 ? "error" : "success";
        return ImmutableMap.of("info", info);
    }

    @Override
    public ImmutableMap<String, String> updateNoteById(QueryNoteReq req) {
        DemandReportAnalyseNoteDO update = new DemandReportAnalyseNoteDO();
        update.setNote(req.getNote());
        update.setUpdater(req.getUserName());
        update.setId(req.getId());
        demandDBHelper.update(update);
        return ImmutableMap.of("info", "success");

    }

    @Override
    public Object getDiffData(QueryIndexDataReq req) {

        WhereContent whereContent = getResoureReportWhere(req);
        {
            if (!Lang.isEmpty(req.getFilterIndexValue())) {
                for (String j : req.getFilterIndexValue()) {
                    whereContent.addAnd("p_index=?", j);
                }
            }
        }

        List<AdsTcresAnnualDemandReportDO> all = ORMUtils.db(ckcldDBHelper)
                .getAll(AdsTcresAnnualDemandReportDO.class, whereContent);
        List<String> filterIndexValue = req.getFilterIndexValue();
        Class<?> clzByIndex = BigReportIdxEnum.getClzByIndex(filterIndexValue.get(0));

        if (clzByIndex == null) {
            return ImmutableMap.of("data", Lang.list());
        }

        if (clzByIndex.equals(ResourceReportALForJson.class)) {
            Function<AdsTcresAnnualDemandReportDO, ResourceReportALForJson> map = (o) -> {
                if (o.getOriData() == null) {
                    return new ResourceReportALForJson();
                }
                ResourceReportALForJson parse = JSON.parse(o.getOriData(),
                        ResourceReportALForJson.class);
                parse.setNum(o.getNum());
                parse.setCore(o.getCores());
                return parse;
            };
            List<ResourceReportALForJson> startData = all.stream()
                    .filter((o) -> Strings.equals(o.getVersion(), req.getStartVersion()))
                    .map(map).collect(Collectors.toList());
            List<ResourceReportALForJson> endData = all.stream()
                    .filter((o) -> Strings.equals(o.getVersion(), req.getEndVersion()))
                    .map(map).collect(Collectors.toList());

            Map<String, List<ResourceReportALForJson>> key1GroupBy = ListUtils.groupBy(startData,
                    (a) -> Strings.join("@",
                            Lang.list(a.getYearMonthText(), a.getIndustry(), a.getCustomerName(), a.getRegion(),
                                    a.getZone(), a.getPlanProduct())));
            Map<String, List<ResourceReportALForJson>> key2GroupBy = ListUtils.groupBy(endData,
                    (a) -> Strings.join("@",
                            Lang.list(a.getYearMonthText(), a.getIndustry(), a.getCustomerName(), a.getRegion(),
                                    a.getZone(), a.getPlanProduct())));

            Set<String> allKey = new HashSet<>();
            allKey.addAll(key1GroupBy.keySet());
            allKey.addAll(key2GroupBy.keySet());
            List<DiffItem> ret = Lang.list();
            for (String key : allKey.toArray(new String[0])) {
                List<ResourceReportALForJson> v1 = key1GroupBy.get(key);
                List<ResourceReportALForJson> v2 = key2GroupBy.get(key);
                ResourceReportALForJson oneValue;
                if (Lang.isNotEmpty(v1)) {
                    oneValue = v1.get(0);
                } else {
                    oneValue = v2.get(0);
                }
                BigDecimal sum1 = ListUtils.sum(v1, ResourceReportALForJson::getNum);
                BigDecimal sum2 = ListUtils.sum(v2, ResourceReportALForJson::getNum);
                BigDecimal core1 = ListUtils.sum(v1, ResourceReportALForJson::getCore);
                BigDecimal core2 = ListUtils.sum(v2, ResourceReportALForJson::getCore);

                if (sum1.compareTo(sum2) != 0) {
                    DiffItem diffItem = transDiffFrom(oneValue);
                    diffItem.setNum1(sum1);
                    diffItem.setNum2(sum2);
                    diffItem.setCore1(core1);
                    diffItem.setCore2(core2);
                    ret.add(diffItem);
                }
            }
            BigDecimal num1 = ListUtils.sum(ret, DiffItem::getNum1);
            BigDecimal num2 = ListUtils.sum(ret, DiffItem::getNum2);
            BigDecimal core1 = ListUtils.sum(ret, DiffItem::getCore1);
            BigDecimal core2 = ListUtils.sum(ret, DiffItem::getCore2);
            return new QueryForecastDiffRsp(ret, num1, num2, core1, core2);
        }

//        if (clzByIndex.equals(ResourceReportBHCDEForJson.class) || clzByIndex.equals(ResourceReportFGIForJson.class)) {
//            List<String> startKeys = all.stream()
//                    .filter((o) -> Strings.equals(o.getVersion(), req.getStartVersion()))
//                    .map(AdsTcresAnnualDemandReportDO::getOriId)
//                    .collect(Collectors.toList());
//            List<String> endKeys = all.stream()
//                    .filter((o) -> Strings.equals(o.getVersion(), req.getEndVersion()))
//                    .map(AdsTcresAnnualDemandReportDO::getOriId)
//                    .collect(Collectors.toList());
//            // 去除 id 的交集
//            List<String> intersection = ListUtils.intersection(startKeys, endKeys);
//            Map<String, Boolean> intersectionMap = ListUtils.toMap(intersection, (o) -> o, (o) -> true);
//            all = all.stream().filter((o) -> !intersectionMap.containsKey(o.getOriId())).collect(Collectors.toList());
//        }

        if (clzByIndex.equals(ResourceReportBHCDEForJson.class)) {

            Function<AdsTcresAnnualDemandReportDO, ResourceReportBHCDEForJson> parseFunc = (o) -> {
                if (o.getOriData() == null) {
                    return new ResourceReportBHCDEForJson();
                }
                ResourceReportBHCDEForJson parse = JSON.parse(o.getOriData(),
                        ResourceReportBHCDEForJson.class);
                parse.setNum(o.getNum());
                parse.setCore(o.getCores());
                return parse;
            };
            List<ResourceReportBHCDEForJson> startData = all.stream()
                    .filter((o) -> Strings.equals(o.getVersion(), req.getStartVersion()))
                    .map(parseFunc).collect(Collectors.toList());
            List<ResourceReportBHCDEForJson> endData = all.stream()
                    .filter((o) -> Strings.equals(o.getVersion(), req.getEndVersion()))
                    .map(parseFunc).collect(Collectors.toList());

            Function<ResourceReportBHCDEForJson, String> groupByKey = ResourceReportBHCDEForJson::getSubId;

            Map<String, ResourceReportBHCDEForJson> key1GroupBy = ListUtils.toMap(startData, groupByKey,
                    Function.identity());
            Map<String, ResourceReportBHCDEForJson> key2GroupBy = ListUtils.toMap(endData, groupByKey,
                    Function.identity());
            Set<String> allKey = new HashSet<>();
            allKey.addAll(key1GroupBy.keySet());
            allKey.addAll(key2GroupBy.keySet());
            List<QueryOrderDiffRsp.DiffItem> ret = Lang.list();
            for (String key : allKey.toArray(new String[0])) {
                ResourceReportBHCDEForJson v1 = key1GroupBy.get(key);
                ResourceReportBHCDEForJson v2 = key2GroupBy.get(key);
                BigDecimal num1 = v1 == null ? BigDecimal.ZERO : v1.getNum();
                BigDecimal num2 = v2 == null ? BigDecimal.ZERO : v2.getNum();
                BigDecimal core1 = v1 == null ? BigDecimal.ZERO : v1.getCore();
                BigDecimal core2 = v2 == null ? BigDecimal.ZERO : v2.getCore();
                num1 = num1 == null ? BigDecimal.ZERO : num1;
                num2 = num2 == null ? BigDecimal.ZERO : num2;
                core1 = core1 == null ? BigDecimal.ZERO : core1;
                core2 = core2 == null ? BigDecimal.ZERO : core2;
                if (num1.compareTo(num2) != 0 || core1.compareTo(core2) != 0) {
                    QueryOrderDiffRsp.DiffItem tmp = transOrderDiffFrom(v1 == null ? v2 : v1);
                    tmp.setNum1(num1);
                    tmp.setNum2(num2);
                    tmp.setCore1(core1);
                    tmp.setCore2(core2);
                    ret.add(tmp);
                }
            }
            BigDecimal num1 = ListUtils.sum(ret, QueryOrderDiffRsp.DiffItem::getNum1);
            BigDecimal num2 = ListUtils.sum(ret, QueryOrderDiffRsp.DiffItem::getNum2);
            BigDecimal core1 = ListUtils.sum(ret, QueryOrderDiffRsp.DiffItem::getCore1);
            BigDecimal core2 = ListUtils.sum(ret, QueryOrderDiffRsp.DiffItem::getCore2);
            return new QueryOrderDiffRsp(ret, num1, num2, core1, core2);
        }

        if (clzByIndex.equals(ResourceReportFGIForJson.class)) {
            Function<AdsTcresAnnualDemandReportDO, ResourceReportFGIForJson> parseFunc = (o) -> {
                if (o.getOriData() == null) {
                    return new ResourceReportFGIForJson();
                }
                ResourceReportFGIForJson parse = JSON.parse(o.getOriData(),
                        ResourceReportFGIForJson.class);
                parse.setNum(o.getNum());
                parse.setCore(o.getCores());
                return parse;
            };
            List<ResourceReportFGIForJson> startData = all.stream()
                    .filter((o) -> Strings.equals(o.getVersion(), req.getStartVersion()))
                    .map(parseFunc).collect(Collectors.toList());
            List<ResourceReportFGIForJson> endData = all.stream()
                    .filter((o) -> Strings.equals(o.getVersion(), req.getEndVersion()))
                    .map(parseFunc).collect(Collectors.toList());

            Function<ResourceReportFGIForJson, String> groupByKey = ResourceReportFGIForJson::getAssetId;

            Map<String, ResourceReportFGIForJson> key1GroupBy = ListUtils.toMap(startData, groupByKey,
                    Function.identity());
            Map<String, ResourceReportFGIForJson> key2GroupBy = ListUtils.toMap(endData, groupByKey,
                    Function.identity());
            Set<String> allKey = new HashSet<>();
            allKey.addAll(key1GroupBy.keySet());
            allKey.addAll(key2GroupBy.keySet());
            List<QueryOrderDiffRsp.DiffItem> ret = Lang.list();
            for (String key : allKey.toArray(new String[0])) {
                ResourceReportFGIForJson v1 = key1GroupBy.get(key);
                ResourceReportFGIForJson v2 = key2GroupBy.get(key);
                BigDecimal num1 = v1 == null ? BigDecimal.ZERO : v1.getNum();
                BigDecimal num2 = v2 == null ? BigDecimal.ZERO : v2.getNum();
                BigDecimal core1 = v1 == null ? BigDecimal.ZERO : v1.getCore();
                BigDecimal core2 = v2 == null ? BigDecimal.ZERO : v2.getCore();

                num1 = num1 == null ? BigDecimal.ZERO : num1;
                num2 = num2 == null ? BigDecimal.ZERO : num2;
                core1 = core1 == null ? BigDecimal.ZERO : core1;
                core2 = core2 == null ? BigDecimal.ZERO : core2;
                if (num1.compareTo(num2) != 0 || core1.compareTo(core2) != 0) {
                    QueryOrderDiffRsp.DiffItem tmp = transOrderDiffFrom(v1 == null ? v2 : v1);
                    tmp.setNum1(num1);
                    tmp.setNum2(num2);
                    tmp.setCore1(core1);
                    tmp.setCore2(core2);
                    ret.add(tmp);
                }
            }
            BigDecimal num1 = ListUtils.sum(ret, QueryOrderDiffRsp.DiffItem::getNum1);
            BigDecimal num2 = ListUtils.sum(ret, QueryOrderDiffRsp.DiffItem::getNum2);
            BigDecimal core1 = ListUtils.sum(ret, QueryOrderDiffRsp.DiffItem::getCore1);
            BigDecimal core2 = ListUtils.sum(ret, QueryOrderDiffRsp.DiffItem::getCore2);
            QueryOrderDiffRsp queryOrderDiffRsp = new QueryOrderDiffRsp(ret, num1, num2, core1, core2);
            queryOrderDiffRsp.getDimList()
                    .set(1, ImmutableMap.of("title", " 固资编号", "dataIndex", "assetId", "isShow", true));
            return queryOrderDiffRsp;
        }
        return ImmutableMap.of("data", Lang.list());
    }

    /**
     * 获取切片时间和版本的关系，由于依赖db的创建时间字段，只能追溯到 2023-09-25 的切片
     */
    @Override
    @HiSpeedCache(expireSecond = 900)
    public List<Map<String, String>> queryDateVersion() {
        String sql = "select date(create_time) date ,industry_version version from rrp_config where id >216;";
        List<DateVersionVo> verList = rrpDBHelper.getRaw(DateVersionVo.class, sql);
        return calc(verList).stream().map(s -> {
            Map<String, String> map = Maps.newHashMap();
            map.put(s.getDate().toString(), s.getVersion());
            return map;
        }).collect(Collectors.toList());
    }

    private List<DateVersionVo> calc(List<DateVersionVo> verList) {
        List<DateVersionVo> timeVersionData = new ArrayList<>();
        if (verList == null || verList.size() == 0) {
            return timeVersionData;
        }

        // 获取当前日期
        LocalDate now = LocalDate.now();

        // 跟踪每个版本的结束时间
        LocalDate lastEndDate = null;

        for (int i = 0; i < verList.size(); i++) {
            DateVersionVo versionVo = verList.get(i);
            if (i + 1 < verList.size()) {
                lastEndDate = verList.get(i + 1).getDate();
            } else {
                lastEndDate = now;
            }
            LocalDate start = versionVo.getDate();

            while (start.isBefore(lastEndDate)) {
                timeVersionData.add(new DateVersionVo(start, versionVo.getVersion()));
                start = start.plusDays(1);
            }
        }
        return timeVersionData;
    }

    private QueryOrderDiffRsp.DiffItem transOrderDiffFrom(ResourceReportFGIForJson resourceReportFGIForJson) {
        QueryOrderDiffRsp.DiffItem diffItem = new QueryOrderDiffRsp.DiffItem();
        diffItem.setIndustry(resourceReportFGIForJson.getIndustry());
        diffItem.setCustomerName(resourceReportFGIForJson.getCustomerName());
        diffItem.setPlanProduct(resourceReportFGIForJson.getPlanProduct());
        diffItem.setCreateTime(DateUtils.format(resourceReportFGIForJson.getSubmitTime()));
        diffItem.setSubId(resourceReportFGIForJson.getSubId());
        diffItem.setAssetId(resourceReportFGIForJson.getAssetId());
        return diffItem;
    }

    private QueryOrderDiffRsp.DiffItem transOrderDiffFrom(ResourceReportBHCDEForJson resourceReportBHCDEForJson) {
        QueryOrderDiffRsp.DiffItem diffItem = new QueryOrderDiffRsp.DiffItem();
        diffItem.setSubId(resourceReportBHCDEForJson.getSubId());
        diffItem.setPlanProduct(resourceReportBHCDEForJson.getPlanProduct());
        diffItem.setIndustry(resourceReportBHCDEForJson.getIndustry());
        diffItem.setCustomerName(resourceReportBHCDEForJson.getCustomerName());
        diffItem.setCreateTime(resourceReportBHCDEForJson.getCreateTime());
        return diffItem;
    }


    private DiffItem transDiffFrom(ResourceReportALForJson oneValue) {
        DiffItem diffItem = new DiffItem();
        diffItem.setPlanProduct(oneValue.getPlanProduct());
        diffItem.setRegion(oneValue.getRegion());
        diffItem.setZone(oneValue.getZone());
        diffItem.setYearMonthText(oneValue.getYearMonthText());
        diffItem.setCustomerName(oneValue.getCustomerName());
        diffItem.setIndustry(oneValue.getIndustry());
        return diffItem;
    }


    private DemandReportAnalyseNoteDO transNoteFrom(QueryNoteReq req) {
        DemandReportAnalyseNoteDO demandReportAnalyseNoteDO = new DemandReportAnalyseNoteDO();
        demandReportAnalyseNoteDO.setCreator(req.getUserName());
        demandReportAnalyseNoteDO.setStartVersion(req.getStartVersion());
        demandReportAnalyseNoteDO.setEndVersion(req.getEndVersion());
        demandReportAnalyseNoteDO.setStartMonth(req.getStartMonth());
        demandReportAnalyseNoteDO.setEndMonth(req.getEndMonth());
        demandReportAnalyseNoteDO.setFilter(JSON.toJson(req.getFilter()));
        demandReportAnalyseNoteDO.setPIndex(req.getFilterIndexValue().get(0));
        demandReportAnalyseNoteDO.setNote(req.getNote());
        return demandReportAnalyseNoteDO;
    }

    private DateItem transFrom(List<AdsTcresAnnualDemandReportForQueryVO> value, String startVersion,
            String endVersion) {
        DateItem ret = transBaseInfoFrom(value.get(0));

        List<AdsTcresAnnualDemandReportForQueryVO> start = ListUtils.filter(value,
                (o) -> Objects.equals(o.getVersion(), startVersion));
        List<AdsTcresAnnualDemandReportForQueryVO> end = ListUtils.filter(value,
                (o) -> Objects.equals(o.getVersion(), endVersion));
        ret.setCore1(Lang.isEmpty(start) ? BigDecimal.ZERO : start.get(0).getSumCores()
                .setScale(2, RoundingMode.HALF_UP));
        ret.setCore2(Lang.isEmpty(end) ? BigDecimal.ZERO : end.get(0).getSumCores()
                .setScale(2, RoundingMode.HALF_UP));
        ret.setNum1(Lang.isEmpty(start) ? BigDecimal.ZERO : start.get(0).getSumNum()
                .setScale(2, RoundingMode.HALF_UP));
        ret.setNum2(Lang.isEmpty(end) ? BigDecimal.ZERO : end.get(0).getSumNum()
                .setScale(2, RoundingMode.HALF_UP));
        return ret;
    }

    private DateItem transBaseInfoFrom(AdsTcresAnnualDemandReportForQueryVO adsTcresAnnualDemandReportForQueryVO) {
        DateItem dateItem = new DateItem();
        dateItem.setDate(adsTcresAnnualDemandReportForQueryVO.getDate());
        dateItem.setYearMonth(adsTcresAnnualDemandReportForQueryVO.getYearMonth());
        dateItem.setDimBigClass(adsTcresAnnualDemandReportForQueryVO.getDimBigClass());
        dateItem.setDimProductClass(adsTcresAnnualDemandReportForQueryVO.getDimProductClass());
        dateItem.setDimDeviceType(adsTcresAnnualDemandReportForQueryVO.getDimDeviceType());
        dateItem.setDimInstanceType(adsTcresAnnualDemandReportForQueryVO.getDimInstanceType());
        dateItem.setDimCpuType(adsTcresAnnualDemandReportForQueryVO.getDimCpuType());
        dateItem.setDimNetType(adsTcresAnnualDemandReportForQueryVO.getDimNetType());
        dateItem.setDimRegion(adsTcresAnnualDemandReportForQueryVO.getDimRegion());
        dateItem.setDimRegionClass(adsTcresAnnualDemandReportForQueryVO.getDimRegionClass());
        dateItem.setDimIndustry(adsTcresAnnualDemandReportForQueryVO.getDimIndustry());
        dateItem.setDimCustomer(adsTcresAnnualDemandReportForQueryVO.getDimCustomer());
        dateItem.setDimPlanProduct(adsTcresAnnualDemandReportForQueryVO.getDimPlanProduct());
        dateItem.setDimReason1(adsTcresAnnualDemandReportForQueryVO.getDimReason1());
        dateItem.setDimReason2(adsTcresAnnualDemandReportForQueryVO.getDimReason2());
        dateItem.setDimSupplyWay(adsTcresAnnualDemandReportForQueryVO.getDimSupplyWay());
        return dateItem;
    }

    private QueryResourceReportItemRsp transFrom(List<AdsTcresAnnualDemandReportForQueryVO> allData,
            QueryResourceReportItemReq req) {

        List<String> allIndex = getAllIndexAndSort();

        Map<String, List<AdsTcresAnnualDemandReportForQueryVO>> dataGroupByWithoutVersionAndIndex =
                ListUtils.groupBy(allData, (o) -> {
                    List<Object> allDim = getValueByName(AdsTcresAnnualDemandReportForQueryDO.class, req.getDim(), o);
                    return Strings.join("@", allDim);
                });

        List<Data> rows = Lang.list();
        for (Entry<String, List<AdsTcresAnnualDemandReportForQueryVO>> one : dataGroupByWithoutVersionAndIndex.entrySet()) {
            List<AdsTcresAnnualDemandReportForQueryVO> value = one.getValue();
            // 维度数据
            Data data = transFrom(value.get(0));
            // index 数据，列转行
            List<IndexItem> columns = transIndexItemFrom(value, req, allIndex, one.getKey());
            data.setIndexItems(columns);
            rows.add(data);
        }

        // 根据给过来的维度排序
        rows = rows.stream().sorted(Comparator.comparing((o) -> {
            List<Object> allDim = getValueByNameAndReplace(Data.class, req.getDim(), o)
                    .stream().map((one) -> {
                        if (one == null) {
                            return "null";
                        }
                        if (Strings.isBlank(one.toString())) {
                            return "empty";
                        }
                        return one;
                    }).collect(Collectors.toList());
            log.info(Strings.join("-", allDim));
            return Strings.join("-", allDim);
        }, Comparator.nullsLast(String::compareTo))).collect(Collectors.toList());

        List<String> curIndex = allData.stream().map(AdsTcresAnnualDemandReportForQueryDO::getPIndex).distinct()
                .collect(Collectors.toList());
        return new QueryResourceReportItemRsp(rows,
                ListUtils.transform(allIndex, (o) -> ImmutableMap.of(
                        "name", o,
                        "isNull", curIndex.contains(o),
                        "hasDate", QueryDateDetailsRsp.dataInfo.getOrDefault(o, false))));
    }

    private List<String> getAllIndexAndSort() {
        return Arrays.stream(BigReportIdxEnum.values())
                .filter(s -> s != BigReportIdxEnum.UNKNOWN)
                .map(BigReportIdxEnum::getDesc).collect(Collectors.toList());
//        String sql = "select distinct p_index from ads_tcres_demand_annual_excueted_report where isNotNull(p_index)";
//        return allIndex.stream().sorted((c1, c2) -> {
//            List<String> tmpSort = Lang.list("全年需求预测量", "已执行预测", "未执行预测", "云运管对冲（复用）",
//                    "运管对冲量", "ERP采购下单量", "已交付量（交付月份）",
//                    "已交付量（需求月份）", "产品提货量", "在途量（未交付）", "13周预测");
//            for (String s : tmpSort) {
//                if (Strings.equals(c1, s)) {
//                    return -1;
//                }
//                if (Strings.equals(c2, s)) {
//                    return 1;
//                }
//            }
//            return Comparator.nullsLast(String::compareTo).compare(c1, c2);
//        }).collect(Collectors.toList());
    }

    private List<IndexItem> transIndexItemFrom(List<AdsTcresAnnualDemandReportForQueryVO> value,
            QueryResourceReportItemReq req, List<String> allIndex, String prefixKey) {
        List<IndexItem> columns = Lang.list();
        Map<String, List<AdsTcresAnnualDemandReportForQueryVO>> indexColumnDats = ListUtils.groupBy(value,
                AdsTcresAnnualDemandReportForQueryDO::getPIndex);
        for (String index : allIndex) {
            List<AdsTcresAnnualDemandReportForQueryVO> oneIndexColumnDats = indexColumnDats.get(index);
            QueryResourceReportItemRsp.Data.IndexItem oneIndexItem = new IndexItem();
            oneIndexItem.setDim(prefixKey + "#" + index);
            oneIndexItem.setName(index);
            oneIndexItem.setIsNull(true);
            if (oneIndexColumnDats != null) {
                List<AdsTcresAnnualDemandReportForQueryVO> start = ListUtils.filter(oneIndexColumnDats,
                        (o) -> Objects.equals(o.getVersion(), req.getStartVersion()));
                List<AdsTcresAnnualDemandReportForQueryVO> end = ListUtils.filter(oneIndexColumnDats,
                        (o) -> Objects.equals(o.getVersion(), req.getEndVersion()));
                oneIndexItem.setCore1(Lang.isEmpty(start) ? BigDecimal.ZERO : start.get(0).getSumCores()
                        .setScale(2, RoundingMode.HALF_UP));
                oneIndexItem.setCore2(Lang.isEmpty(end) ? BigDecimal.ZERO : end.get(0).getSumCores()
                        .setScale(2, RoundingMode.HALF_UP));
                oneIndexItem.setNum1(Lang.isEmpty(start) ? BigDecimal.ZERO : start.get(0).getSumNum()
                        .setScale(2, RoundingMode.HALF_UP));
                oneIndexItem.setNum2(Lang.isEmpty(end) ? BigDecimal.ZERO : end.get(0).getSumNum()
                        .setScale(2, RoundingMode.HALF_UP));
                oneIndexItem.setIsNull(false);
            }
            columns.add(oneIndexItem);
        }
        return columns;
    }

    @SneakyThrows
    private List<Object> getValueByNameAndReplace(Class<?> clz, List<String> reqGroupValues, Object data) {
        ArrayList<Object> ret = Lang.list();
        if (Lang.isEmpty(reqGroupValues)) {
            return ret;
        }
        for (String reqGroupValue : reqGroupValues) {
            Field declaredField = clz.getDeclaredField(reqGroupValue);
            Object value = DOInfoReader.getValue(declaredField, data);
            // 大类排序，替换排序字符
            if (Strings.equals(reqGroupValue, "dimBigClass")) {
                Map<Object, Object> map = ImmutableMap.builder()
                        .put("计算", 1).put("存储", 2).put("网络", 3).put("公有云其他", 4)
                        .put("私有云", 5).put("Webank", 6).put("滴滴打车", 7).build();
                Object orDefault = map.getOrDefault(value == null ? "" : value, "8" + value);
                ret.add(orDefault.toString());
                continue;
            }
            ret.add(value);
        }
        return ret;
    }

    @SneakyThrows
    private List<Object> getValueByName(Class<?> clz, List<String> reqGroupValues, Object data) {
        ArrayList<Object> ret = Lang.list();
        if (Lang.isEmpty(reqGroupValues)) {
            return ret;
        }
        for (String reqGroupValue : reqGroupValues) {
            Field declaredField = clz.getDeclaredField(reqGroupValue);
            Object value = DOInfoReader.getValue(declaredField, data);
            ret.add(value);
        }
        return ret;
    }

    private Data transFrom(AdsTcresAnnualDemandReportForQueryDO adsTcresAnnualDemandReportForQueryDO) {
        Data data = new Data();
        data.setYearMonth(adsTcresAnnualDemandReportForQueryDO.getYearMonth());
        data.setDimBigClass(adsTcresAnnualDemandReportForQueryDO.getDimBigClass());
        data.setDimProductClass(adsTcresAnnualDemandReportForQueryDO.getDimProductClass());
        data.setDimDeviceType(adsTcresAnnualDemandReportForQueryDO.getDimDeviceType());
        data.setDimInstanceType(adsTcresAnnualDemandReportForQueryDO.getDimInstanceType());
        data.setDimCpuType(adsTcresAnnualDemandReportForQueryDO.getDimCpuType());
        data.setDimNetType(adsTcresAnnualDemandReportForQueryDO.getDimNetType());
        data.setDimRegion(adsTcresAnnualDemandReportForQueryDO.getDimRegion());
        data.setDimRegionClass(adsTcresAnnualDemandReportForQueryDO.getDimRegionClass());
        data.setDimIndustry(adsTcresAnnualDemandReportForQueryDO.getDimIndustry());
        data.setDimCustomer(adsTcresAnnualDemandReportForQueryDO.getDimCustomer());
        data.setDimPlanProduct(adsTcresAnnualDemandReportForQueryDO.getDimPlanProduct());
        data.setDimReason1(adsTcresAnnualDemandReportForQueryDO.getDimReason1());
        data.setDimReason2(adsTcresAnnualDemandReportForQueryDO.getDimReason2());
        data.setDimSupplyWay(adsTcresAnnualDemandReportForQueryDO.getDimSupplyWay());
        return data;
    }
}
