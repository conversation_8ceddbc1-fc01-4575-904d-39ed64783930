package cloud.demand.app.modules.industry_report.service.mrp.buy;

import cloud.demand.app.modules.industry_report.model.enums.IndustryMRPReportTargetEnum;
import cloud.demand.app.modules.industry_report.model.vo.MRPTargetVO;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.RecursiveTask;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class FailBuyResourceNum extends BaseBuyInfo{

    @Override
    public IndustryMRPReportTargetEnum token() {
        return IndustryMRPReportTargetEnum.FAIL_BUY_RESOURCE_NUM;
    }

    @Override
    public Set<IndustryMRPReportTargetEnum> sonTargets() {
        return null;
    }

    @Override
    public RecursiveTask<Object> task(Map<String, Object> initContext) {
        FailBuyResourceNum task = new FailBuyResourceNum();
        task.init(initContext);
        return task;
    }

    @Override
    protected Object compute() {
        List<GroupTencentCloudBuyInfo> rawData = loadData();

        List<MRPTargetVO> result = rawData.stream().map(r -> {
           MRPTargetVO vo = GroupTencentCloudBuyInfo.toVO(r, content.getProduct());
           if (content.getProduct().equals("CVM")) {
               if (!r.getBizType().equals("cvm") || r.getFailCoreNum().compareTo(BigDecimal.ZERO) == 0) {
                   return null;
               }
           } else {
               if (!r.getBizType().equals("gpu") || r.getFailGpuNum().compareTo(BigDecimal.ZERO) == 0) {
                   return null;
               }
           }
           vo.setCoreNum(r.getFailCoreNum());
           vo.setGpuNum(r.getFailGpuNum());
           return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        addToResult(result);
        return result;
    }
}
