package cloud.demand.app.entity.cubes;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

@Data
@ToString
@Table("sto_rmdb_servers")
public class StoRmdbServersDO {

    /**切片日期*/
    @Column(value = "DAY")
    private LocalDate day;

    /** 服务器ID<br/>Column: [server_id] */
    @Column(value = "Id")
    private Long id;

    /** 物理固资<br/>Column: [PhysicsPcCode] */
    @Column(value = "PhysicsPcCode")
    private String physicsPcCode;

    /** 逻辑固资<br/>Column: [LogicPcCode] */
    @Column(value = "LogicPcCode")
    private String logicPcCode;

    /** 上架日期<br/>Column: [GoUpDate] */
    @Column(value = "GoUpDate")
    private String goUpDate;

    /** 上架月数<br/>Column: [MonthCount] */
    @Column(value = "MonthCount")
    private Integer monthCount;

    /** 上架年限<br/>Column: [ServerLife] */
    @Column(value = "ServerLife")
    private Integer serverLife;

    /** 设备类型<br/>Column: [DeviceType] */
    @Column(value = "DeviceType")
    private String deviceType;

    /** GPU卡类型<br/>Column: [GpuCardCategory] */
    @Column(value = "GpuCardCategory")
    private String gpuCardCategory;

    /** GPU卡数量<br/>Column: [StrGpuNumber] */
    @Column(value = "StrGpuNumber")
    private Integer strGpuNumber;

    /** GPU型号<br/>Column: [StrGpuModel] */
    @Column(value = "StrGpuModel")
    private String strGpuModel;

    /** Module业务类型<br/>Column: [BusinessType] */
    @Column(value = "BusinessType")
    private String businessType;

    /** OBS业务分类<br/>Column: [ObsBusinessType] */
    @Column(value = "ObsBusinessType")
    private String obsBusinessType;

    /** OBS事业群简称<br/>Column: [ObsBgShortName] */
    @Column(value = "ObsBgShortName")
    private String obsBgShortName;

    /** OBS事业群<br/>Column: [ObsBgName] */
    @Column(value = "ObsBgName")
    private String obsBgName;

    /** OBS虚拟部门<br/>Column: [ObsDept] */
    @Column(value = "ObsDept")
    private String obsDept;

    /** 业务部门<br/>Column: [BelongDpName] */
    @Column(value = "BelongDpName")
    private String belongDpName;

    /** 运维部门<br/>Column: [BelongDept] */
    @Column(value = "BelongDept")
    private String belongDept;

    /** 规划产品<br/>Column: [PlanProductName] */
    @Column(value = "PlanProductName")
    private String planProductName;

    /** 产品集<br/>Column: [ProductSetName] */
    @Column(value = "ProductSetName")
    private String productSetName;

    /** 运营产品<br/>Column: [BelongPdName] */
    @Column(value = "BelongPdName")
    private String belongPdName;

    /** 业务集<br/>Column: [BelongBnName] */
    @Column(value = "BelongBnName")
    private String belongBnName;

    /** 业务<br/>Column: [Bussiness2Name] */
    @Column(value = "Bussiness2Name")
    private String bussiness2Name;

    /** 业务模块<br/>Column: [Bussiness3Name] */
    @Column(value = "Bussiness3Name")
    private String bussiness3Name;

    /** 需求属性<br/>Column: [demandAttributes] */
    @Column(value = "demandAttributes")
    private String demandAttributes;

    /** 物料属性<br/>Column: [materialAttributes] */
    @Column(value = "materialAttributes")
    private String materialAttributes;

    /** 服务器销售属性<br/>Column: [StrHotSell] */
    @Column(value = "StrHotSell")
    private String strHotSell;

    /** 设备来源<br/>Column: [Source] */
    @Column(value = "Source")
    private String source;

    /** 是否故障<br/>Column: [isBreakdown] */
    @Column(value = "isBreakdown")
    private String isBreakdown;

    /** 是否可搬迁<br/>Column: [ServerCanMove] */
    @Column(value = "ServerCanMove")
    private String serverCanMove;

    /** 地区<br/>Column: [AreaType] */
    @Column(value = "AreaType")
    private String areaType;

    /** 国家<br/>Column: [CountryName] */
    @Column(value = "CountryName")
    private String countryName;

    /** 国内Region<br/>Column: [RegionName] */
    @Column(value = "RegionName")
    private String regionName;

    /** Zone<br/>Column: [ZoneName] */
    @Column(value = "ZoneName")
    private String zoneName;

    /** 城市<br/>Column: [BelongCity] */
    @Column(value = "BelongCity")
    private String belongCity;

    /** Campus<br/>Column: [SubZoneName] */
    @Column(value = "SubZoneName")
    private String subZoneName;

    /** 是否过保<br/>Column: [isOverProtection] */
    @Column(value = "isOverProtection")
    private String isOverProtection;

    /** 设备网络类型<br/>Column: [DeviceNetType] */
    @Column(value = "DeviceNetType")
    private String deviceNetType;

    /** 内网IP<br/>Column: [serverLanIP] */
    @Column(value = "serverLanIP")
    private String serverLanIP;

    /** 外网IP<br/>Column: [serverWanIP] */
    @Column(value = "serverWanIP")
    private String serverWanIP;

    /** 处理器厂家<br/>Column: [cpu_vender] */
    @Column(value = "cpu_vender")
    private String cpuVender;

    /** 处理器逻辑核数<br/>Column: [cpu_logic_core] */
    @Column(value = "cpu_logic_core")
    private Integer cpuLogicCore;

    /** ERP业务分类<br/>Column: [erpBusinessType] */
    @Column(value = "erpBusinessType")
    private String erpBusinessType;

    /** 维护人员<br/>Column: [Maintainer] */
    @Column(value = "Maintainer")
    private String maintainer;

    /** 归属管理单元名称<br/>Column: [BelongUnitName] */
    @Column(value = "BelongUnitName")
    private String belongUnitName;

    /** cmdb状态<br/>Column: [CMDBStatus] */
    @Column(value = "CMDBStatus")
    private String cmdbStatus;

    /** 设备描述<br/>Column: [StrDescription] */
    @Column(value = "StrDescription")
    private String strDescription;

    /** 标准型号<br/>Column: [ServerModel] */
    @Column(value = "ServerModel")
    private String serverModel;

    /** 服务器版本<br/>Column: [SvrTypeVersion] */
    @Column(value = "SvrTypeVersion")
    private String svrTypeVersion;


}
