select p.project_type,
       p.biz_dept,
       p.mod_biz_type,
       p.plan_product,
       p.country,
       p.city_name,
       p.campus,
       p.device_type,
       p.year,
       p.month,
       p.week,
       p.id,
       e.order_id,
       e.execute_num num
from res_plan.res_plan_execute_bak e
         left join res_plan.res_plan_for_procurement p on p.plan_id = e.plan_id and p.snapshot_version = e.snapshot_version
where e.snapshot_version = ?
  and p.year >= ?
  and e.execute_num > 0