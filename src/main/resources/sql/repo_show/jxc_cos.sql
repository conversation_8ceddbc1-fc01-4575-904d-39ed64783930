select category,
       CASE
           indicator_code
           WHEN 'E1' THEN '资源转进'
           WHEN 'NO1' THEN '资源转进'
           WHEN 'E2' THEN '资源转出盘活'
           WHEN 'NO2' THEN '资源转出盘活'
           WHEN 'RT' THEN '资源转出盘活'
           ELSE indicator_name
           ENd as     p_name,
       null    as     start_val,
       null    as     end_val,
       SUM(logic_num) diff
from report_cvm_jxc
where product_type = 'COS'
  and deleted = 0
  and stat_time BETWEEN '{date_start_af}' and '{date_end}'
  and indicator_code in ('C1', 'C2', 'E1', 'NO1', 'E2', 'NO2', 'M', 'RT')
group by category,
         p_name
union all
SELECT CASE
           indicator_code
           WHEN 'P2PRATE' THEN ''
           WHEN 'SRATE' THEN ''
           ELSE category
           END                                                                                             as category,
       CASE
           indicator_code
           WHEN 'fc' THEN '缓删'
           WHEN 'yg' THEN 'Yotta系统消耗'
           when 'P' then '线下库存'
           ELSE indicator_name
           ENd                                                                                             as p_name,
       sum(IF(stat_time = '{date_start}', logic_num, 0))                                                   as start_num,
       SUM(IF(stat_time = '{date_end}', logic_num, 0))                                                     as end_num,
       sum(IF(stat_time = '{date_end}', logic_num, 0)) - SUM(IF(stat_time = '{date_start}', logic_num, 0)) as diff
from report_cvm_jxc
where stat_time in ('{date_start}', '{date_end}')
  and product_type = 'COS'
  and deleted = 0
  and indicator_code in ('a', 'c', 'K', 'e', 'f', 'P', 'Q', 'fc', 'yg', 'OR', 'NS', 'opd')
group by category,
         p_name
union all
SELECT CASE
           indicator_code
           WHEN 'P2PRATE' THEN ''
           WHEN 'SRATE' THEN ''
           ELSE category
           END                                                                                             as category,
       indicator_name                                                                                      as p_name,
       sum(IF(stat_time = '{date_start}', logic_num, 0))                                                   as start_num,
       SUM(IF(stat_time = '{date_end}', logic_num, 0))                                                     as end_num,
       sum(IF(stat_time = '{date_end}', logic_num, 0)) - SUM(IF(stat_time = '{date_start}', logic_num, 0)) as diff
from report_cvm_jxc
where stat_time in ('{date_start}', '{date_end}')
  and product_type = 'COS'
  and deleted = 0
  and indicator_code in ('P2PRATE', 'SRATE')
  and customhouse_title = ''
group by category,
         p_name


ORDER BY CASE
             category
             WHEN '进' THEN 0
             WHEN '销' THEN 1
             WHEN '存' THEN 2
             WHEN '其他' THEN 3
             ELSE 4
             END,
         CASE
             p_name
             WHEN '采购提货-新增采购' THEN 0
             WHEN '采购提货-库存复用' THEN 1
             WHEN '资源转进' THEN 2
             WHEN '资源转出盘活' THEN 3
             WHEN '总进货' THEN 4
             WHEN '外部计费规模' THEN 1
             WHEN '内部售卖规模' THEN 2
             WHEN '销汇总' THEN 3
             WHEN '线上好料' THEN 1
             WHEN '线上差料' THEN 2
             WHEN '线下库存' THEN 3
             WHEN '库存汇总' THEN 4
             WHEN '提前删除量' THEN 1
             WHEN '缓删' THEN 2
             WHEN 'Yotta系统消耗' THEN 3
             WHEN '存储副本冗余' THEN 4
             WHEN '非可售' THEN 5
             WHEN '端到端利用率' THEN 6
             WHEN '售卖利用率' THEN 7
             END
       